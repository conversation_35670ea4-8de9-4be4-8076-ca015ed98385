import akshare as ak
from matplotlib import pyplot as plt
from pybroker.ext.data import AKShare
import pybroker
import riskfolio as rp
# 获取板块名称
# stock_board_industry_name_em_df = ak.stock_board_industry_name_em()
# print(stock_board_industry_name_em_df)
# print(stock_board_industry_name_em_df['板块名称'].values)
# 获取板块数据
stock_board_industry_cons_df = ak.stock_board_industry_cons_em(
    symbol="汽车整车")
print(stock_board_industry_cons_df)
#  获取前12个市盈率最高的股票
stock_car = stock_board_industry_cons_df.sort_values(
    by='市盈率-动态', ascending=False).iloc[0:12, :]
print(stock_car)

pybroker.enable_data_source_cache('akshare')
akshare = AKShare()

df = akshare.query(symbols=stock_car['代码'].values,
                   start_date='3/1/2021',
                   end_date='3/1/2023',
                   adjust="hfq",
                   timeframe="1d",
                   )

df_car = df.merge(stock_car[["代码", "名称"]],
                  left_on="symbol", right_on="代码", how="left")
print(df_car)
# 获取股票价格,构建新表
# bfill 填充后一个值，ffill 填充前一个值
stock_close = df_car.pivot(index="date", columns="名称",
                           values="close").bfill().ffill()

#  构建收益率表
Y_all = stock_close.pct_change().dropna()
print(Y_all)

Y = Y_all
# 构建投资组合
port = rp.Portfolio(returns=Y)

# 选择风险度量
rm = 'MSV'  # 半标准差

# 估计模型的输入（历史估计）
method_mu = 'hist'  # 基于历史数据估计预期收益的方法
method_cov = 'hist'  # 基于历史数据估计协方差矩阵的方法
port.assets_stats(method_mu=method_mu, method_cov=method_cov)

# 估计最大化风险收益比的投资组合
w1 = port.optimization(model='Classic', rm=rm,
                       rf=0, obj='Sharpe',
                       l=0,
                       hist=True,
                       )
# 估计有效前沿 (均值-半标准差)上的点,w1是最大的风险收益比
ws = port.efficient_frontier(
    model='Classic', rm=rm, points=20, rf=0, hist=True,)

# 估计半标准差的风险平价投资组合
w2 = port.rp_optimization(model='Classic', rm=rm,
                          rf=0,
                          b=None,
                          hist=True,
                          )
print(w1)

label = 'Max Risk Adjusted Return Portfolio'
mu = port.mu
cov = port.cov
returns = port.returns

ax = rp.plot_frontier(w_frontier=ws,
                      mu=mu,
                      cov=cov,
                      returns=returns,
                      rm=rm,
                      rf=0,
                      alpha=0.05,
                      cmap='viridis',
                      w=w1,
                      label=label,
                      marker='*',
                      s=16,
                      c='r',
                      height=6,
                      width=10,
                      t_factor=252,
                      ax=None)


# 构建股票价格除以初始价格,归一化
stock_close_div0 = stock_close.div(stock_close.iloc[0, :], axis=1)
# 画图
plt.rcParams['font.sans-serif'] = ['SimHei']
fig = plt.figure(figsize=(18, 6))
stock_close_div0.plot(ax=plt.gca())
plt.show()
