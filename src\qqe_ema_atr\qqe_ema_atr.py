import pandas as pd
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext, FeeMode
import matplotlib.pyplot as plt
from pybroker.vect import cross
import pybroker.indicator
import talib
import pandas_ta as ta
import akshare as ak
# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

# 使用talib自定义indicator


def buy(ctx: ExecContext):
    long_cond, short_cond, long_trail, short_trail = wilders_rsi(ctx)
    # 判断短期均线上穿长期均线，收盘价格高于典型价格，收盘价格高于平均价格，则买入
    buy_signal = long_cond
    # 判断长期均线上穿短期均线，则卖出
    sell_signal = short_cond
    if not ctx.long_pos() and buy_signal:
        ctx.buy_limit_price = ctx.close[-1]*1.095
        ctx.buy_shares = ctx.calc_target_shares(0.5)
        ctx.score = ctx.volume[-1]
        ctx.stop_loss_pct = long_trail
        ctx.stop_profit_pct = short_trail
        print('买入', ctx.dt, buy_signal)
    if ctx.long_pos() and sell_signal:
        ctx.sell_all_shares()
        print('卖出', ctx.dt, sell_signal)

    return


def vwap(data):
    price = (data.close + data.high + data.low)/3
    vwap = (price * data.volume).cumsum() / data.volume.cumsum()
    # print(ta.vwap(data.high, data.low, data.close, data.volume))
    return vwap


start_date = '2020-01-01'
end_date = '2023-01-01'


def wilders_rsi(data, wilders=14, rsi_len=14, thresh=3.0, ema_len=14,
                atr_len=14,
                trailing_mult=1.5, vol_len=20):
    close_df = pd.Series(data.close)
    rsi = ta.rsi(close_df, length=rsi_len)  # 计算RSI值
    wrsi = ta.rma(rsi, wilders)  # 使用过wilders平滑方法处理RSI
    delta = abs(wrsi - wrsi.mean())  # 计算RSI的绝对变化值
    avg_delta = ta.rma(delta, wilders)  # 对绝对变化值进行wilders平滑处理
    trailing_line = wrsi - avg_delta * thresh  # 计算追踪线
    # 根据RSI和追踪线判断信号，1表示买进，-1表示卖出，0表示无信号
    signal = 0
    if wrsi.iloc[-1] > trailing_line.iloc[-1]:
        signal = 1
    if wrsi.iloc[-1] < trailing_line.iloc[-1]:
        signal = -1
    # 计算EMA
    ema = ta.ema(close_df, ema_len)
    # 计算平均K线收盘价
    heikin_close = (data.open + data.high + data.low + data.close) / 4
    # 判断是否为 多头趋势
    bullTrend = data.close[-1] > ema.iloc[-1] and heikin_close[-1] > ema.iloc[-1]
    bearTrend = data.close[-1] < ema.iloc[-1]

    # 计算成交量均值
    volSMA = ta.sma(pd.Series(data.volume), vol_len)
    # 判断成交量是否高于均值
    volOk = data.volume[-1] > volSMA.iloc[-1]

    long_cond = signal == 1 and bullTrend and volOk
    short_cond = signal == -1 and bearTrend and volOk

    atr = ta.atr(pd.Series(data.high), pd.Series(
        data.low), pd.Series(data.close), atr_len)
    # 计算多头止损点位
    long_trail = abs(atr.iloc[-1] * trailing_mult * 100)
    short_trail = abs(atr.iloc[-1] * trailing_mult * 100)
    return long_cond, short_cond, long_trail, short_trail


def strategy1():
    akshare = AKShare()

    # 创建策略，设置初始资金为500000
    config = StrategyConfig(
        initial_cash=500_000, fee_mode=FeeMode.ORDER_PERCENT, fee_amount=0.005, subtract_fees=True)
    #  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
    strategy = Strategy(data_source=akshare, start_date=start_date,
                        end_date=end_date, config=config)
    #  添加执行函数，应用股票代码
    strategy.add_execution(buy, ['000001.SZ', '002594.SZ'], indicators=[])
    # 进行回测，前 20 天作为预热期
    result = strategy.backtest(warmup=69)
    print(result.trades)
    print(result.metrics_df)
    cash_value = result.portfolio.equity/result.portfolio.equity.iloc[0]
    cash_value.plot(
        label=f"strategy")
    # chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
    # chart.plot(result.portfolio.index, result.portfolio['market_value'])


def plot300(symbol="000300"):
    # 沪深300作为参考
    index_zh_a_hist_df = ak.index_zh_a_hist(
        symbol=symbol, period="daily", start_date=start_date, end_date=end_date)
    index_zh_a_hist_df["日期"] = pd.to_datetime(index_zh_a_hist_df["日期"])
    index_zh_a_hist_df.set_index("日期", inplace=True)

    index_close = index_zh_a_hist_df["收盘"]
    index_time_scale = index_close/index_close.iloc[0]
    index_time_scale.plot(figsize=(18, 4), label=symbol)


# 210%
strategy1()
plot300()
plot300('000001')
plot300('002594')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.legend()
plt.show()
