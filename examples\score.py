import pandas as pd
from typing import Mapping
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')


def buy_low(ctx: ExecContext):
    #  判断是否持有仓位
    if not tuple(ctx.long_positions()):
        ctx.buy_shares = ctx.calc_target_shares(0.5)
        ctx.hold_bars = 10
        # 通过交易量设置分数，分数越高，则优先级越高
        ctx.score = ctx.volume[-1]
    return
def print_bank(ctxs: Mapping[str, ExecContext]):
    # 打印分数前5的标的
    symbols = []
    scores = []
    for ctx in ctxs.values():
        symbols.append(ctx.symbol)
        scores.append(ctx.score)
    v_rank = pd.DataFrame({"symbol": symbols, "score": scores}).sort_values(by="score", ascending=False)
    print(v_rank.head(5))
    return False


akshare = AKShare()


# 创建策略，设置初始资金为500000, 最大允许的持仓数量为1
config = StrategyConfig(initial_cash=500_000, max_long_positions=2)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=akshare, start_date='1/1/2020',
                    end_date='12/31/2023', config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(buy_low, ['000001.SZ', '000002.SZ', '000003.SZ', '000004.SZ', '000005.SZ'])

strategy.set_after_exec(print_bank)

# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=15)
print(result.trades)
print(result.metrics_df)
chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
