import pybroker
from pybroker import Strategy, StrategyConfig, ExecContext
from pybroker.ext.data import AKShare
import numpy as np

# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')


def buy_low(ctx):
    if ctx.long_pos():
        return
    if ctx.bars >= 2 and ctx.close[-1] < ctx.low[-2]:
        ctx.buy_shares = ctx.calc_target_shares(0.25)
        ctx.buy_limit_price = ctx.close[-1] - 0.01
        ctx.hold_bars = 3


def short_high(ctx):
    if ctx.short_pos():
        return
    if ctx.bars >= 2 and ctx.close[-1] > ctx.high[-2]:
        ctx.sell_shares = 100
        ctx.hold_bars = 2


def pos_size_handler(ctx: ExecContext):
    # 获取所有买入信号
    signals = tuple(ctx.signals("buy"))
    # 如果没有买入信号，则返回
    if not signals:
        return
    # 获取逆波动率

    def get_inverse_volatility(signal): return 1 / \
        np.std(signal.bar_data.close[-100:])
    # 计算所有的逆波动率之和
    total_inverse_volatility = sum(map(get_inverse_volatility, signals))
    for signal in signals:
        # 计算每个信号的权重
        size = get_inverse_volatility(signal) / total_inverse_volatility
        # Calculate the number of shares given the latest close price.
        # 计算买入的股数
        shares = ctx.calc_target_shares(
            size, signal.bar_data.close[-1], cash=95_000)
        ctx.set_shares(signal, shares)


akshare = AKShare()

config = StrategyConfig(initial_cash=500_000, bootstrap_sample_size=100)
strategy = Strategy(akshare, '3/1/2017', '3/1/2022', config)
strategy.set_pos_size_handler(pos_size_handler)
strategy.add_execution(buy_low, ['000001.SZ', '000002.SZ'])
strategy.add_execution(short_high, ['000003.SZ'])
# 自助法通过在从回测收益率抽取的随机样本上反复计算指标来工作。
# 然后，在每个随机样本上计算指标，并取平均值。
# 通过在数千个随机样本上进行这样的操作，我们可以获得更稳健、更准确的指标估计。
result = strategy.backtest(calc_bootstrap=True)
# （ 利润因子 和 夏普比率）的 置信区间
# 97.5% 的把握认为夏普比率在给定值 x 或更高。
# 如果夏普比率的下界为负，利润因子的下界小于 1，这表明策略是不可靠的
print(result.bootstrap.conf_intervals)

# 自助法检查策略的最大回撤
# 99.9% 置信水平下的自助法最大回撤为-14.267505

print(result.bootstrap.drawdown_conf)
