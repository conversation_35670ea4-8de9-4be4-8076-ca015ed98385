import sqlite3
import pandas as pd
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
from pybroker import highest, lowest, Indicator
from pybroker.data import DataSource


class DFDataSource(DataSource):
    def __init__(self, data):
        super().__init__()
        pybroker.register_columns('pe')
        self.data = data

    def _fetch_data(self, symbols, start_date, end_date, _timeframe, _adjust):
        df = self.data
        df = df[df['symbol'].isin(symbols)]
        return df[(df['date'] >= start_date) & (df['date'] <= end_date)]


def buy_low(ctx: ExecContext):
    high = ctx.high[-11:-1].max()
    low = ctx.low[-11:-1].min()

    if not ctx.long_pos() and ctx.close[-1] > ctx.low[-1]*1.0 and ctx.low[-1] < low:
        ctx.buy_shares = ctx.calc_target_shares(1)
        ctx.stop_loss_pct = 8
        ctx.stop_profit_pct = 15
    if ctx.long_pos() and ctx.close[-1] < ctx.high[-1] * 0.9 and ctx.high[-1] < high:
        ctx.sell_all_shares()
    return


conn = sqlite3.connect(
    r'E:\workspace\python\pybroker-test\data\stock_2018_daily\stock_2018.db')
stock_daily0 = pd.read_sql(
    'select * from stock_daily where 交易日期>"20200101" and 交易日期<"20210101"', conn)
stock_daily0.head()
# 转换为日期格式
stock_daily0["交易日期"] = pd.to_datetime(stock_daily0["交易日期"].astype("str"))

pyb_data_pe = stock_daily0[["交易日期", "股票代码", "开盘价",
                            "最高价", "最低价", "收盘价", "成交量(手)", "市盈率(静态)"]]
pyb_data_pe.columns = ["date", "symbol", "open",
                       "high", "low", "close", "volume", "pe"]
data_source = DFDataSource(pyb_data_pe)
# dataSource的好处是可以调用dataSouce上的方法
print(data_source.query(['000001.SZ'], '2020-01-01', '2021-01-01').head())
# 创建策略，设置初始资金为500000
config = StrategyConfig(initial_cash=500_000)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=data_source, start_date='1/1/2020',
                    end_date='1/1/2021', config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(buy_low, ['000001.SZ'], indicators=[
                       highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10)])
# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=15)
print(result.trades)
print(result.metrics_df)
chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
