import sqlite3
import pandas as pd
import pybroker
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
import akshare as ak

# 因子排序的轮动交易实现


def rand_ld(ctx: ExecContext):
    #  判断是否持有仓位
    if ctx.long_pos():
        # 如果某只股票不在symbols中，则卖出
        if pybroker.param("symbols") is not None and ctx.symbol not in pybroker.param("symbols"):
            ctx.sell_all_shares()
    else:
        # 涨停板禁止买入
        # 设置今天价格比昨日价格低于9.5%，则买入
        ctx.buy_limit_price = ctx.close[-1]*1.095
        ctx.buy_shares = ctx.calc_target_shares(0.2)
        ctx.hold_bars = 10
        # 通过交易量和换手率设置分数，分数越高，则优先级越高
        ctx.score = -ctx.pe[-1]
    return

count = 0
# 根据排名平衡仓位
def set_rank_symbols(ctxs: dict[str, ExecContext]) -> bool:
    # dt = tuple(ctxs.values())[0].dt
    # print(dt)
    count = pybroker.param("count")
    if count is None:
        count = 0
    if count <= 5:
        count = count+1
        pybroker.param("count", count)
        return True
    pybroker.param("count", 0)
    symbols = []
    pe = []
    for ctx in ctxs.values():
        symbols.append(ctx.symbol)
        pe.append(-ctx.pe[-1])

    v_rank = pd.DataFrame({"symbol": symbols, "pe": pe}
                          ).sort_values(by="pe", ascending=True)
    pybroker.param("symbols", v_rank.head(5).symbol.values)
    return False


conn = sqlite3.connect(
    r'E:\workspace\python\pybroker-test\data\stock_2018_daily\stock_2018.db')
stock_daily0 = pd.read_sql(
    'select * from stock_daily where 股票代码>"003000.SZ"', conn)
print('交易日期范围', stock_daily0['交易日期'].min(), stock_daily0['交易日期'].max())

# 转换为日期格式
stock_daily0["交易日期"] = pd.to_datetime(stock_daily0["交易日期"].astype("str"))
pyb_data_pe = stock_daily0[["交易日期", "股票代码", "开盘价",
                            "最高价", "最低价", "收盘价", "成交量(手)",
                            "市盈率(静态)",
                            "换手率(%)"
                            ]]
pyb_data_pe.columns = ["date", "symbol", "open",
                       "high", "low", "close", "volume", "pe", "tr"]
pybroker.register_columns('pe')
pybroker.register_columns('tr')


# 创建策略，设置初始资金为500000
config = StrategyConfig(initial_cash=500_000, max_long_positions=10)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=pyb_data_pe, start_date='2018-02-22',
                    end_date='2023-02-10', config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(rand_ld, pyb_data_pe.symbol.unique())
strategy.set_before_exec(set_rank_symbols)
# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=15)

print(result.trades)
print(result.metrics_df)

# 沪深300作为参考
index_zh_a_hist_df = ak.index_zh_a_hist(
    symbol="000300", period="daily", start_date="20180222", end_date="20230210")
index_zh_a_hist_df["日期"] = pd.to_datetime(index_zh_a_hist_df["日期"])
index_zh_a_hist_df.set_index("日期", inplace=True)

index_close = index_zh_a_hist_df["收盘"]
index_time_scale = index_close/index_close.iloc[0]
index_time_scale.plot(figsize=(18, 4))

cash_value = result.portfolio.equity/result.portfolio.equity.iloc[0]
cash_value.plot()

# chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
# chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
