import sqlite3
import pandas as pd
import pybroker
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
import akshare as ak
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split 
from sklearn.tree import DecisionTreeRegressor
from sklearn.metrics import r2_score
from sklearn.metrics import mean_absolute_error
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import GradientBoostingRegressor

def func0(x):
    return x.pct_change().shift(-1)


conn = sqlite3.connect(
    r'E:\workspace\python\pybroker-test\data\stock_2018_daily\stock_2018.db')
stock_daily1 = pd.read_sql(
    "select * from stock_daily where 股票代码<'003000.SZ'", con=conn)
stock_daily1["交易日期"] = pd.to_datetime(stock_daily1["交易日期"].astype(str))

print('交易日期范围', stock_daily1['交易日期'].min(), stock_daily1['交易日期'].max())

stock_daily1.columns = ['index', "date", "symbol", '股票简称', "open", "high", "low", "close", "volume",
                        '成交额(千元)', '换手率(%)', '量比', '市盈率(静态)', '市盈率(TTM)', '市盈率(动态)', '市净率',
                        '市销率', '市销率(TTM)', '股息率(%)', '股息率(TTM)(%)', '总股本(万股)', '流通股本(万股)',
                        '总市值(万元)', '流通市值(万元)']

stock_daily1["return_s1"] = stock_daily1.groupby(
    "symbol", group_keys=False).close.apply(func0)

# 机器学习
xy = stock_daily1[["open", "high", "low", "close", "volume",
                        '成交额(千元)', '换手率(%)', '量比', '市盈率(静态)', '市盈率(TTM)', '市盈率(动态)', '市净率',
                        '市销率', '市销率(TTM)', '股息率(%)', '股息率(TTM)(%)', '总股本(万股)', '流通股本(万股)',
                        '总市值(万元)', '流通市值(万元)', "return_s1"]].dropna()
xy_x = xy[["open", "high", "low", "close", "volume",
                        '成交额(千元)', '换手率(%)', '量比', '市盈率(静态)', '市盈率(TTM)', '市盈率(动态)', '市净率',
                        '市销率', '市销率(TTM)', '股息率(%)', '股息率(TTM)(%)', '总股本(万股)', '流通股本(万股)',
                        '总市值(万元)', '流通市值(万元)']].values
xy_y = xy["return_s1"].values

x1,x2,y1,y2=train_test_split(xy_x,xy_y,test_size=0.7)#分割数据出训练集与测试集，0.7是两者行数的比例

clf = GradientBoostingRegressor()
#clf = LinearRegression()
clf = clf.fit(x1,y1)

clf.predict(x2)
# 等于0随便拆，接近1则预测准确
print(r2_score(y2,clf.predict(x2)))
print(mean_absolute_error(y2,clf.predict(x2)))

