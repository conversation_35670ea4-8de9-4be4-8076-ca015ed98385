from typing import Dict, Mapping
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
from pybroker import highest, lowest, Indicator
# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

# 当前股票上下文


def buy_low(ctx: ExecContext):
    high = ctx.high[-11:-1].max()
    low = ctx.low[-11:-1].min()
    ctx.session['buy'] = False
    ctx.session['sell'] = False
    if not ctx.long_pos() and ctx.close[-1] > ctx.low[-1]*1.05 and ctx.low[-1] < low:
        ctx.buy_shares = ctx.calc_target_shares(0.5)
        ctx.session['buy'] = True
        ctx.stop_loss_pct = 8
        ctx.stop_profit_pct = 15
    if ctx.long_pos() and ctx.close[-1] < ctx.high[-1] * 0.97 and ctx.high[-1] < high:
        ctx.sell_all_shares()
        ctx.session['sell'] = True
        print('卖出')
    return


def start_of_month(ctxs: Mapping[str, ExecContext]):
    dt = tuple(ctxs.values())[0].dt
    # 月份不等时，就是开始，更新当前月份
    if dt.month != pybroker.param('current_month'):
        pybroker.param('current_month', dt.month)
        return True
    return False

# 设置每只股票的配置


def set_target_shares(ctxs: Mapping[str, ExecContext], target_shares: Dict[str, float]):
    for symbol, ctx in ctxs.items():
        target_share = ctx.calc_target_shares(target_shares[symbol])
        pos = ctx.long_pos()
        hasBuyOrSell = ctx.session['buy'] == True or ctx.session['sell'] == True
        if hasBuyOrSell:
            print('已操作')
            return
        #  没有持仓时，直接买入，有持仓就多卖少买
        if pos is None:
            ctx.buy_shares = target_share
        elif pos.shares < target_share:
            ctx.buy_shares = target_share - pos.shares
        elif pos.shares > target_share:
            ctx.sell_shares = pos.shares - target_share
    return

# 平衡仓位
# 键是股票代码，值是上下文


def rebalance(ctxs: Mapping[str, ExecContext]):
    # 每个月月初，将每种资产的配置调整为相等
    if start_of_month(ctxs):
        target = 1 / len(ctxs)
        # 设置每只股票的配置
        set_target_shares(ctxs, {symbol: target for symbol in ctxs.keys()})


akshare = AKShare()

# You can substitute 000001.SZ with 000001, and it will still work!
# and you can set start_date as "20210301" format
# You can also set adjust to 'qfq' or 'hfq' to adjust the data,
# and set timeframe to '1d', '1w' to get daily, weekly data
# df = akshare.query(
#     symbols=['000001.SZ', '600000.SH'],
#     start_date='3/1/2021',
#     end_date='3/1/2026',
#     adjust="",
#     timeframe="1d",
# )
# print(df)


# 创建策略，设置初始资金为500000
config = StrategyConfig(initial_cash=500_000)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=akshare, start_date='1/1/2022',
                    end_date='1/1/2023',  config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(buy_low, ['000001.SZ', '002594.SZ'], indicators=[
                       highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10)])
# 设置回测结束后的函数,每个交易日执行一次
strategy.set_after_exec(rebalance)
# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=15)
print(result.trades)
print(result.metrics_df)
chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
