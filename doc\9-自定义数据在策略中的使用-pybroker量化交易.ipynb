{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 自定义数据在pyborker策略中的使用\n", "\n", "https://www.pybroker.com/zh-cn/latest/notebooks/7.%20Creating%20a%20Custom%20Data%20Source.html"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["<diskcache.core.Cache at 0x1641c48b2d0>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pybroker\n", "from pybroker.ext.data import AKShare\n", "from pybroker import ExecContext, StrategyConfig, Strategy\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import riskfolio as rp\n", "import akshare as ak\n", "import pandas as pd\n", "import numpy as np\n", "import sqlite3\n", "import datetime\n", "\n", "import talib\n", "from pybroker.vect import cross\n", "\n", "#正常显示画图时出现的中文和负号\n", "from pylab import mpl\n", "\n", "mpl.rcParams['font.sans-serif']=['SimHei']\n", "mpl.rcParams['axes.unicode_minus']=False\n", "\n", "akshare = AKShare()\n", "\n", "pybroker.enable_data_source_cache('akshare')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1.16.60'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["ak.__version__"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## pybroker的数据形式"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded cached bar data.\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-03-02</td>\n", "      <td>000001.SZ</td>\n", "      <td>3653.44</td>\n", "      <td>3739.58</td>\n", "      <td>3594.93</td>\n", "      <td>3658.31</td>\n", "      <td>1473425</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-03-03</td>\n", "      <td>000001.SZ</td>\n", "      <td>3646.94</td>\n", "      <td>3890.73</td>\n", "      <td>3627.43</td>\n", "      <td>3879.35</td>\n", "      <td>1919635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-03-04</td>\n", "      <td>000001.SZ</td>\n", "      <td>3840.34</td>\n", "      <td>3957.36</td>\n", "      <td>3820.84</td>\n", "      <td>3864.72</td>\n", "      <td>1213579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-03-05</td>\n", "      <td>000001.SZ</td>\n", "      <td>3828.97</td>\n", "      <td>3874.48</td>\n", "      <td>3692.44</td>\n", "      <td>3770.46</td>\n", "      <td>880171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-03-08</td>\n", "      <td>000001.SZ</td>\n", "      <td>3786.71</td>\n", "      <td>3827.34</td>\n", "      <td>3630.68</td>\n", "      <td>3650.19</td>\n", "      <td>993774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>967</th>\n", "      <td>2023-02-23</td>\n", "      <td>600000.SH</td>\n", "      <td>77.41</td>\n", "      <td>77.61</td>\n", "      <td>77.28</td>\n", "      <td>77.28</td>\n", "      <td>114365</td>\n", "    </tr>\n", "    <tr>\n", "      <th>968</th>\n", "      <td>2023-02-24</td>\n", "      <td>600000.SH</td>\n", "      <td>77.22</td>\n", "      <td>77.48</td>\n", "      <td>76.95</td>\n", "      <td>76.95</td>\n", "      <td>166811</td>\n", "    </tr>\n", "    <tr>\n", "      <th>969</th>\n", "      <td>2023-02-27</td>\n", "      <td>600000.SH</td>\n", "      <td>76.82</td>\n", "      <td>77.08</td>\n", "      <td>76.82</td>\n", "      <td>76.82</td>\n", "      <td>158006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>970</th>\n", "      <td>2023-02-28</td>\n", "      <td>600000.SH</td>\n", "      <td>76.95</td>\n", "      <td>77.08</td>\n", "      <td>76.69</td>\n", "      <td>76.95</td>\n", "      <td>174481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>971</th>\n", "      <td>2023-03-01</td>\n", "      <td>600000.SH</td>\n", "      <td>76.89</td>\n", "      <td>77.54</td>\n", "      <td>76.89</td>\n", "      <td>77.48</td>\n", "      <td>256613</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>972 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          date     symbol     open     high      low    close   volume\n", "0   2021-03-02  000001.SZ  3653.44  3739.58  3594.93  3658.31  1473425\n", "1   2021-03-03  000001.SZ  3646.94  3890.73  3627.43  3879.35  1919635\n", "2   2021-03-04  000001.SZ  3840.34  3957.36  3820.84  3864.72  1213579\n", "3   2021-03-05  000001.SZ  3828.97  3874.48  3692.44  3770.46   880171\n", "4   2021-03-08  000001.SZ  3786.71  3827.34  3630.68  3650.19   993774\n", "..         ...        ...      ...      ...      ...      ...      ...\n", "967 2023-02-23  600000.SH    77.41    77.61    77.28    77.28   114365\n", "968 2023-02-24  600000.SH    77.22    77.48    76.95    76.95   166811\n", "969 2023-02-27  600000.SH    76.82    77.08    76.82    76.82   158006\n", "970 2023-02-28  600000.SH    76.95    77.08    76.69    76.95   174481\n", "971 2023-03-01  600000.SH    76.89    77.54    76.89    77.48   256613\n", "\n", "[972 rows x 7 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = akshare.query(\n", "    symbols=['000001.SZ', '600000.SH'],\n", "    start_date='3/2/2021',\n", "    end_date='3/1/2023',\n", "    adjust=\"hfq\",\n", "    timeframe=\"1d\",\n", ")\n", "df"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def trying_strategy(ctx: ExecContext):\n", "    high = ctx.high[-11:-1].max()\n", "    low = ctx.low[-11:-1].min()\n", "    #print(high, low)\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > high:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        #print(ctx.dt, \"buy\")\n", "    \n", "    if ctx.long_pos() and ctx.close[-1] < low:\n", "        ctx.sell_all_shares()\n", "        #print(ctx.dt, \"sell\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 22% (161 of 728) |####                  | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 37% (271 of 728) |########              | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 61% (451 of 728) |#############         | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 89% (651 of 728) |###################   | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:04\n"]}], "source": ["strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["#result.trades"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 获取本地数据转化为pybroker数据形式"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>交易日期</th>\n", "      <th>股票代码</th>\n", "      <th>股票简称</th>\n", "      <th>开盘价</th>\n", "      <th>最高价</th>\n", "      <th>最低价</th>\n", "      <th>收盘价</th>\n", "      <th>成交量(手)</th>\n", "      <th>成交额(千元)</th>\n", "      <th>...</th>\n", "      <th>市盈率(动态)</th>\n", "      <th>市净率</th>\n", "      <th>市销率</th>\n", "      <th>市销率(TTM)</th>\n", "      <th>股息率(%)</th>\n", "      <th>股息率(TTM)(%)</th>\n", "      <th>总股本(万股)</th>\n", "      <th>流通股本(万股)</th>\n", "      <th>总市值(万元)</th>\n", "      <th>流通市值(万元)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6807</td>\n", "      <td>20200102</td>\n", "      <td>000002.SZ</td>\n", "      <td>万科A</td>\n", "      <td>4867.9136</td>\n", "      <td>4986.6432</td>\n", "      <td>4824.8741</td>\n", "      <td>4832.2947</td>\n", "      <td>1012130.40</td>\n", "      <td>3342373.870</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>2.2000</td>\n", "      <td>1.2362</td>\n", "      <td>1.0649</td>\n", "      <td>3.2098</td>\n", "      <td>3.2098</td>\n", "      <td>1.130214e+06</td>\n", "      <td>971517.0043</td>\n", "      <td>3.679978e+07</td>\n", "      <td>3.163259e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6808</td>\n", "      <td>20200103</td>\n", "      <td>000002.SZ</td>\n", "      <td>万科A</td>\n", "      <td>4854.5565</td>\n", "      <td>4869.3977</td>\n", "      <td>4716.5334</td>\n", "      <td>4756.6046</td>\n", "      <td>805536.29</td>\n", "      <td>2584309.903</td>\n", "      <td>...</td>\n", "      <td>14.89</td>\n", "      <td>2.1655</td>\n", "      <td>1.2169</td>\n", "      <td>1.0482</td>\n", "      <td>3.2608</td>\n", "      <td>3.2608</td>\n", "      <td>1.130214e+06</td>\n", "      <td>971517.0043</td>\n", "      <td>3.622337e+07</td>\n", "      <td>3.113712e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6809</td>\n", "      <td>20200106</td>\n", "      <td>000002.SZ</td>\n", "      <td>万科A</td>\n", "      <td>4712.0810</td>\n", "      <td>4713.5651</td>\n", "      <td>4637.8750</td>\n", "      <td>4676.4621</td>\n", "      <td>876840.58</td>\n", "      <td>2761448.649</td>\n", "      <td>...</td>\n", "      <td>14.64</td>\n", "      <td>2.1290</td>\n", "      <td>1.1964</td>\n", "      <td>1.0306</td>\n", "      <td>3.3167</td>\n", "      <td>3.3167</td>\n", "      <td>1.130214e+06</td>\n", "      <td>971517.0043</td>\n", "      <td>3.561305e+07</td>\n", "      <td>3.061250e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6810</td>\n", "      <td>20200107</td>\n", "      <td>000002.SZ</td>\n", "      <td>万科A</td>\n", "      <td>4691.3033</td>\n", "      <td>4738.7952</td>\n", "      <td>4652.7162</td>\n", "      <td>4713.5651</td>\n", "      <td>577933.43</td>\n", "      <td>1827510.871</td>\n", "      <td>...</td>\n", "      <td>14.76</td>\n", "      <td>2.1459</td>\n", "      <td>1.2058</td>\n", "      <td>1.0387</td>\n", "      <td>3.2906</td>\n", "      <td>3.2906</td>\n", "      <td>1.130214e+06</td>\n", "      <td>971517.0043</td>\n", "      <td>3.589561e+07</td>\n", "      <td>3.085538e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>6811</td>\n", "      <td>20200108</td>\n", "      <td>000002.SZ</td>\n", "      <td>万科A</td>\n", "      <td>4645.2956</td>\n", "      <td>4715.0492</td>\n", "      <td>4608.1926</td>\n", "      <td>4701.6922</td>\n", "      <td>529996.84</td>\n", "      <td>1667143.803</td>\n", "      <td>...</td>\n", "      <td>14.72</td>\n", "      <td>2.1405</td>\n", "      <td>1.2028</td>\n", "      <td>1.0361</td>\n", "      <td>3.2989</td>\n", "      <td>3.2989</td>\n", "      <td>1.130214e+06</td>\n", "      <td>971517.0043</td>\n", "      <td>3.580519e+07</td>\n", "      <td>3.077766e+07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 24 columns</p>\n", "</div>"], "text/plain": ["   index      交易日期       股票代码 股票简称        开盘价        最高价        最低价  \\\n", "0   6807  20200102  000002.SZ  万科A  4867.9136  4986.6432  4824.8741   \n", "1   6808  20200103  000002.SZ  万科A  4854.5565  4869.3977  4716.5334   \n", "2   6809  20200106  000002.SZ  万科A  4712.0810  4713.5651  4637.8750   \n", "3   6810  20200107  000002.SZ  万科A  4691.3033  4738.7952  4652.7162   \n", "4   6811  20200108  000002.SZ  万科A  4645.2956  4715.0492  4608.1926   \n", "\n", "         收盘价      成交量(手)      成交额(千元)  ...  市盈率(动态)     市净率     市销率  市销率(TTM)  \\\n", "0  4832.2947  1012130.40  3342373.870  ...      NaN  2.2000  1.2362    1.0649   \n", "1  4756.6046   805536.29  2584309.903  ...    14.89  2.1655  1.2169    1.0482   \n", "2  4676.4621   876840.58  2761448.649  ...    14.64  2.1290  1.1964    1.0306   \n", "3  4713.5651   577933.43  1827510.871  ...    14.76  2.1459  1.2058    1.0387   \n", "4  4701.6922   529996.84  1667143.803  ...    14.72  2.1405  1.2028    1.0361   \n", "\n", "   股息率(%)  股息率(TTM)(%)       总股本(万股)     流通股本(万股)       总市值(万元)      流通市值(万元)  \n", "0  3.2098       3.2098  1.130214e+06  971517.0043  3.679978e+07  3.163259e+07  \n", "1  3.2608       3.2608  1.130214e+06  971517.0043  3.622337e+07  3.113712e+07  \n", "2  3.3167       3.3167  1.130214e+06  971517.0043  3.561305e+07  3.061250e+07  \n", "3  3.2906       3.2906  1.130214e+06  971517.0043  3.589561e+07  3.085538e+07  \n", "4  3.2989       3.2989  1.130214e+06  971517.0043  3.580519e+07  3.077766e+07  \n", "\n", "[5 rows x 24 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["conn=sqlite3.connect(r'I:\\量化金融\\stock_2018.db')\n", "stock_daily0=pd.read_sql(\"select * from stock_daily where 交易日期>'20200101' and 交易日期<'20210101' \",con=conn)\n", "stock_daily0.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['index', '交易日期', '股票代码', '股票简称', '开盘价', '最高价', '最低价', '收盘价', '成交量(手)',\n", "       '成交额(千元)', '换手率(%)', '量比', '市盈率(静态)', '市盈率(TTM)', '市盈率(动态)', '市净率',\n", "       '市销率', '市销率(TTM)', '股息率(%)', '股息率(TTM)(%)', '总股本(万股)', '流通股本(万股)',\n", "       '总市值(万元)', '流通市值(万元)'],\n", "      dtype='object')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["stock_daily0.columns"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["stock_daily0[\"交易日期\"]=pd.to_datetime(stock_daily0[\"交易日期\"].astype(str))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["pyb_data=stock_daily0[[\"交易日期\",\"股票代码\",\"开盘价\",\"最高价\",\"最低价\",\"收盘价\",\"成交量(手)\"]]\n", "pyb_data.columns=[\"date\",\"symbol\",\"open\",\"high\",\"low\",\"close\",\"volume\"]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-01-02</td>\n", "      <td>000002.SZ</td>\n", "      <td>4867.9136</td>\n", "      <td>4986.6432</td>\n", "      <td>4824.8741</td>\n", "      <td>4832.2947</td>\n", "      <td>1012130.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-01-03</td>\n", "      <td>000002.SZ</td>\n", "      <td>4854.5565</td>\n", "      <td>4869.3977</td>\n", "      <td>4716.5334</td>\n", "      <td>4756.6046</td>\n", "      <td>805536.29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-01-06</td>\n", "      <td>000002.SZ</td>\n", "      <td>4712.0810</td>\n", "      <td>4713.5651</td>\n", "      <td>4637.8750</td>\n", "      <td>4676.4621</td>\n", "      <td>876840.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-01-07</td>\n", "      <td>000002.SZ</td>\n", "      <td>4691.3033</td>\n", "      <td>4738.7952</td>\n", "      <td>4652.7162</td>\n", "      <td>4713.5651</td>\n", "      <td>577933.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-01-08</td>\n", "      <td>000002.SZ</td>\n", "      <td>4645.2956</td>\n", "      <td>4715.0492</td>\n", "      <td>4608.1926</td>\n", "      <td>4701.6922</td>\n", "      <td>529996.84</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date     symbol       open       high        low      close  \\\n", "0 2020-01-02  000002.SZ  4867.9136  4986.6432  4824.8741  4832.2947   \n", "1 2020-01-03  000002.SZ  4854.5565  4869.3977  4716.5334  4756.6046   \n", "2 2020-01-06  000002.SZ  4712.0810  4713.5651  4637.8750  4676.4621   \n", "3 2020-01-07  000002.SZ  4691.3033  4738.7952  4652.7162  4713.5651   \n", "4 2020-01-08  000002.SZ  4645.2956  4715.0492  4608.1926  4701.6922   \n", "\n", "       volume  \n", "0  1012130.40  \n", "1   805536.29  \n", "2   876840.58  \n", "3   577933.43  \n", "4   529996.84  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["pyb_data.head()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-04-01 00:00:00 to 2021-01-01 00:00:00\n", "\n", "Test split: 2020-04-01 00:00:00 to 2020-12-31 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 185) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 65% (121 of 185) |##############        | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (185 of 185) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["strategy = Strategy(pyb_data, start_date='2020-04-01', end_date='2021-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>9.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>100000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>165394.46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>61363.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>4030.67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   name      value\n", "0           trade_count       9.00\n", "1  initial_market_value  100000.00\n", "2      end_market_value  165394.46\n", "3             total_pnl   61363.79\n", "4        unrealized_pnl    4030.67"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.head()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='date'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result.portfolio.equity.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 引入指标"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["pyb_data_pe=stock_daily0[[\"交易日期\",\"股票代码\",\"开盘价\",\"最高价\",\"最低价\",\"收盘价\",\"成交量(手)\",\"市盈率(静态)\"]]\n", "pyb_data_pe.columns=[\"date\",\"symbol\",\"open\",\"high\",\"low\",\"close\",\"volume\",\"pe\"]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["pybroker.register_columns('pe')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["44.6277"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["pyb_data_pe.pe.median()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['000504.SZ', '000505.SZ', '000506.SZ', '000507.SZ', '000509.SZ',\n", "       '000510.SZ', '000513.SZ', '000514.SZ', '000516.SZ', '000517.SZ',\n", "       '000518.SZ', '000519.SZ', '000520.SZ', '000521.SZ', '000523.SZ',\n", "       '000524.SZ', '000525.SZ', '000526.SZ', '000528.SZ', '000529.SZ'],\n", "      dtype=object)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["pyb_data_pe.symbol.unique()[100:120]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def trying_strategy_pe(ctx: ExecContext):\n", "    high = ctx.high[-11:-1].max()\n", "    low = ctx.low[-11:-1].min()\n", "    #print(ctx.pe[-1])\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > high and ctx.pe[-1] < 20:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "    \n", "    if ctx.long_pos() and ctx.close[-1] < low:\n", "        ctx.sell_all_shares()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-04-01 00:00:00 to 2021-01-01 00:00:00\n", "\n", "Test split: 2020-04-01 00:00:00 to 2020-12-31 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 185) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 16% (31 of 185) |###                    | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 38% (71 of 185) |########               | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 65% (121 of 185) |##############        | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (185 of 185) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["pybroker.register_columns('pe')\n", "strategy = Strategy(pyb_data_pe, start_date='2020-04-01', end_date='2021-01-01')\n", "strategy.add_execution(trying_strategy_pe, pyb_data_pe.symbol.unique()[100:120])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>1.100000e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>1.000000e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>1.182277e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>1.822773e+04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>-3.637979e-12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   name         value\n", "0           trade_count  1.100000e+01\n", "1  initial_market_value  1.000000e+05\n", "2      end_market_value  1.182277e+05\n", "3             total_pnl  1.822773e+04\n", "4        unrealized_pnl -3.637979e-12"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.head()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='date'>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result.portfolio.equity.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 自定义数据源类"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pybroker.data import DataSource\n", "\n", "class DFDataSource(DataSource):\n", "    def __init__(self, data):\n", "        super().__init__()\n", "        pybroker.register_columns('pe')\n", "        self.data = data\n", "\n", "    def _fetch_data(self, symbols, start_date, end_date, _timeframe, _adjust):\n", "        df = self.data\n", "        df = df[df['symbol'].isin(symbols)]\n", "        return df[(df['date'] >= start_date) & (df['date'] <= end_date)]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["pyb_data_pe=stock_daily0[[\"交易日期\",\"股票代码\",\"开盘价\",\"最高价\",\"最低价\",\"收盘价\",\"成交量(手)\",\"市盈率(静态)\"]]\n", "pyb_data_pe.columns=[\"date\",\"symbol\",\"open\",\"high\",\"low\",\"close\",\"volume\",\"pe\"]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-04-01 00:00:00 to 2021-01-01 00:00:00\n", "\n", "Loading bar data...\n", "Loaded bar data: 0:00:01 \n", "\n", "Test split: 2020-04-01 00:00:00 to 2020-12-31 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 185) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 11% (21 of 185) |##                     | Elapsed Time: 0:00:00 ETA:   0:00:01\n", " 16% (31 of 185) |###                    | Elapsed Time: 0:00:00 ETA:   0:00:01\n", " 22% (41 of 185) |#####                  | Elapsed Time: 0:00:00 ETA:   0:00:01\n", " 32% (61 of 185) |#######                | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 43% (81 of 185) |##########             | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 54% (101 of 185) |############          | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 65% (121 of 185) |##############        | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 76% (141 of 185) |################      | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 87% (161 of 185) |###################   | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 97% (181 of 185) |##################### | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (185 of 185) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:02\n"]}], "source": ["data_source = DFDataSource(pyb_data_pe)\n", "strategy = Strategy(data_source,'2020-04-01','2021-01-01')\n", "strategy.add_execution(trying_strategy_pe, pyb_data_pe.symbol.unique()[100:220])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>31.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>100000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>95740.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>-9110.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>4851.21</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   name      value\n", "0           trade_count      31.00\n", "1  initial_market_value  100000.00\n", "2      end_market_value   95740.66\n", "3             total_pnl   -9110.55\n", "4        unrealized_pnl    4851.21"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.head()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded cached bar data.\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>pe</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>543680</th>\n", "      <td>2020-01-02</td>\n", "      <td>000001.SZ</td>\n", "      <td>1817.6638</td>\n", "      <td>1850.4146</td>\n", "      <td>1806.7470</td>\n", "      <td>1841.6810</td>\n", "      <td>1530231.87</td>\n", "      <td>13.1911</td>\n", "    </tr>\n", "    <tr>\n", "      <th>543681</th>\n", "      <td>2020-01-03</td>\n", "      <td>000001.SZ</td>\n", "      <td>1849.3229</td>\n", "      <td>1889.7154</td>\n", "      <td>1847.1395</td>\n", "      <td>1875.5234</td>\n", "      <td>1116194.81</td>\n", "      <td>13.4335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>543682</th>\n", "      <td>2020-01-06</td>\n", "      <td>000001.SZ</td>\n", "      <td>1856.9647</td>\n", "      <td>1892.9905</td>\n", "      <td>1846.0478</td>\n", "      <td>1863.5148</td>\n", "      <td>862083.50</td>\n", "      <td>13.3475</td>\n", "    </tr>\n", "    <tr>\n", "      <th>543683</th>\n", "      <td>2020-01-07</td>\n", "      <td>000001.SZ</td>\n", "      <td>1870.0650</td>\n", "      <td>1886.4403</td>\n", "      <td>1850.4146</td>\n", "      <td>1872.2484</td>\n", "      <td>728607.56</td>\n", "      <td>13.4101</td>\n", "    </tr>\n", "    <tr>\n", "      <th>543684</th>\n", "      <td>2020-01-08</td>\n", "      <td>000001.SZ</td>\n", "      <td>1855.8730</td>\n", "      <td>1861.3314</td>\n", "      <td>1815.4805</td>\n", "      <td>1818.7555</td>\n", "      <td>847824.12</td>\n", "      <td>13.0269</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             date     symbol       open       high        low      close  \\\n", "543680 2020-01-02  000001.SZ  1817.6638  1850.4146  1806.7470  1841.6810   \n", "543681 2020-01-03  000001.SZ  1849.3229  1889.7154  1847.1395  1875.5234   \n", "543682 2020-01-06  000001.SZ  1856.9647  1892.9905  1846.0478  1863.5148   \n", "543683 2020-01-07  000001.SZ  1870.0650  1886.4403  1850.4146  1872.2484   \n", "543684 2020-01-08  000001.SZ  1855.8730  1861.3314  1815.4805  1818.7555   \n", "\n", "            volume       pe  \n", "543680  1530231.87  13.1911  \n", "543681  1116194.81  13.4335  \n", "543682   862083.50  13.3475  \n", "543683   728607.56  13.4101  \n", "543684   847824.12  13.0269  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["data_source.query(['000001.SZ','002594.SZ'], '2020-01-01', '2020-01-31').head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}