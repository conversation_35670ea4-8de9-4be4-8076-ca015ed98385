import sqlite3
import pandas as pd
import pybroker
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
import akshare as ak
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeRegressor
from sklearn.metrics import r2_score
from sklearn.metrics import mean_absolute_error
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import GradientBoostingRegressor
import talib


def func0(x):
    return x.pct_change().shift(-1)


def train_slr0(symbol, train_data, test_data):
    x1 = train_data[factor]
    y1 = train_data['return_s1']
    # model = DecisionTreeRegressor()
    model = GradientBoostingRegressor()
    # model = LinearRegression()
    model.fit(x1, y1)

    x2 = test_data[factor]
    y2 = test_data['return_s1']
    y_pred = model.predict(x2)
    r2 = r2_score(y2, y_pred)
    print(symbol, f'R^2={r2}')

    return model, factor


def hold_long(ctx):
    if not ctx.long_pos():
        # 预测值大于0，则购买
        if ctx.preds('slr')[-1] > 0.:
            ctx.buy_shares = ctx.calc_target_shares(0.5)
    else:
        #  预测值小于0，则卖出
        if ctx.preds('slr')[-1] < 0.:
            ctx.sell_all_shares()
    return
# 自定义多个特征


def compute_bb(stock_data):

    high, mid, low = talib.BBANDS(stock_data, timeperiod=20)

    x, y, z = talib.MACD(stock_data, fastperiod=12,
                         slowperiod=26, signalperiod=9)

    dongliang = talib.MOM(stock_data, timeperiod=10)

    rsi_6 = talib.RSI(stock_data, timeperiod=6)
    rsi_24 = talib.RSI(stock_data, timeperiod=24)

    up = talib.MAX(stock_data, 20)
    down = talib.MIN(stock_data, 20)

    return_30 = stock_data.pct_change(30)
    return_5 = stock_data.pct_change(5)
    return_10 = stock_data.pct_change(10)

    df = pd.concat([high, low, x, y, z, dongliang, rsi_6, rsi_24,
                   up, down, return_30, return_5, return_10], axis=1)
    df.columns = ["b_high", "b_low", "MACD_x", "MACD_y", "MACD_z", "dong10",
                  "rsi_6", "rsi_24", "up", "down", "return_30", "return_5", "return_10"]

    return df


conn = sqlite3.connect(
    r'E:\workspace\python\pybroker-test\data\stock_2018_daily\stock_2018.db')
stock_daily1 = pd.read_sql(
    "select * from stock_daily where 股票代码<'003000.SZ'", con=conn)
stock_daily1["交易日期"] = pd.to_datetime(stock_daily1["交易日期"].astype(str))

stock_daily1.columns = ['index', "date", "symbol", '股票简称', "open", "high", "low", "close", "volume",
                        '成交额(千元)', '换手率(%)', '量比', '市盈率(静态)', '市盈率(TTM)', '市盈率(动态)', '市净率',
                        '市销率', '市销率(TTM)', '股息率(%)', '股息率(TTM)(%)', '总股本(万股)', '流通股本(万股)',
                        '总市值(万元)', '流通市值(万元)']

stock_daily1["return_s1"] = stock_daily1.groupby(
    "symbol", group_keys=False).close.apply(func0)

# 自定义特征
stock_daily1['SMA20_close'] = stock_daily1.groupby(
    "symbol", group_keys=False).close.apply(lambda x: talib.SMA(x, 20))

# 添加多个特征
z = stock_daily1.groupby("symbol", group_keys=False).close.apply(compute_bb)
stock_daily1 = stock_daily1.join(z)


stock_daily1["close-o"] = stock_daily1["close"]-stock_daily1["open"]
stock_daily1["high-l"] = stock_daily1["high"]-stock_daily1["low"]

factor = stock_daily1.columns[4:].tolist()
factor.remove("return_s1")

# 设置训练方法，每个股票只运行一次
model_slr = pybroker.model('slr', train_slr0)

pyb_data_pe = stock_daily1.drop(columns=['股票简称']).dropna()

pybroker.register_columns(factor[5:]+['return_s1'])
# 抽样方法
config = StrategyConfig(bootstrap_sample_size=100)
strategy = Strategy(pyb_data_pe, '2018-02-22', '2023-02-10', config)

strategy.clear_executions()
strategy.add_execution(hold_long, ['000001.SZ', '002594.SZ'], models=model_slr)

# 向前测试
result = strategy.walkforward(
    warmup=20,
    # 窗口数量
    windows=3,
    train_size=0.5,
    # 预测步长
    lookahead=1,
    calc_bootstrap=True
)
# train_size=0.5 表示50%作为训练
# result = strategy.backtest(train_size=0.5)

print(result.trades)
print(result.metrics_df)

# 沪深300作为参考
index_zh_a_hist_df = ak.index_zh_a_hist(
    symbol="000300", period="daily", start_date="20180222", end_date="20230210")
index_zh_a_hist_df["日期"] = pd.to_datetime(index_zh_a_hist_df["日期"])
index_zh_a_hist_df.set_index("日期", inplace=True)

index_close = index_zh_a_hist_df["收盘"]
index_time_scale = index_close/index_close.iloc[0]
index_time_scale.plot(figsize=(18, 4))

cash_value = result.portfolio.equity/result.portfolio.equity.iloc[0]
cash_value.plot()

# chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
# chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
