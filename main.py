import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
from pybroker import highest, lowest, Indicator
# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')


def buy_low(ctx: ExecContext):
    high = ctx.high[-11:-1].max()
    low = ctx.low[-11:-1].min()

    if not ctx.long_pos() and ctx.close[-1] > ctx.low[-1]*1.05 and ctx.low[-1] < low:
        ctx.buy_shares = ctx.calc_target_shares(1)
        ctx.stop_loss_pct = 8
        ctx.stop_profit_pct = 15
    if ctx.long_pos() and ctx.close[-1] < ctx.high[-1] * 0.97 and ctx.high[-1] < high:
        ctx.sell_all_shares()
    return


akshare = AKShare()

# You can substitute 000001.SZ with 000001, and it will still work!
# and you can set start_date as "20210301" format
# You can also set adjust to 'qfq' or 'hfq' to adjust the data,
# and set timeframe to '1d', '1w' to get daily, weekly data
# df = akshare.query(
#     symbols=['000001.SZ', '600000.SH'],
#     start_date='3/1/2021',
#     end_date='3/1/2026',
#     adjust="",
#     timeframe="1d",
# )
# print(df)


# 创建策略，设置初始资金为500000
config = StrategyConfig(initial_cash=500_000)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=akshare, start_date='1/1/2020',
                    end_date='12/31/2023', config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(buy_low, ['000001.SZ'], indicators=[
                       highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10)])
# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=15)
print(result.trades)
print(result.metrics_df)
chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
