import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
from pybroker.vect import cross
import pybroker.indicator
import talib

# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

# 使用talib自定义indicator


def buy_use_macd(ctx: ExecContext):
    macd_10 = ctx.indicator('macd_10')
    macd_30 = ctx.indicator('macd_30')
    # 判断短期均线上穿长期均线，则买入
    buy_signal = cross(macd_10, macd_30)[-1]
    # 判断长期均线上穿短期均线，则卖出
    sell_signal = cross(macd_30, macd_10)[-1]
    if not ctx.long_pos() and buy_signal:
        ctx.buy_shares = ctx.calc_target_shares(0.5)
        print('买入', ctx.dt, buy_signal)
    if ctx.long_pos() and sell_signal:
        ctx.sell_all_shares()
        print('卖出', ctx.dt, sell_signal)

    return


# 自定义indicator


def macd(data):
    return talib.MACD(data.close, fastperiod=12, slowperiod=26, signalperiod=9)


akshare = AKShare()

# 创建策略，设置初始资金为500000
config = StrategyConfig(initial_cash=500_000)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=akshare, start_date='1/1/2022',
                    end_date='1/1/2023', config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(buy_use_macd, ['000001.SZ', '002594.SZ'], indicators=[
                       pybroker.indicator(
                           'macd_10', lambda data: macd(data)[0]),
                       pybroker.indicator('macd_30', lambda data: macd(data)[1])])
# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=30)
print(result.trades)
print(result.metrics_df)
chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
