import pandas as pd
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext, FeeMode
import matplotlib.pyplot as plt
from pybroker.vect import cross
import pybroker.indicator
import talib
import pandas_ta as ta
import akshare as ak
# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

# 使用talib自定义indicator


# def buy(ctx: ExecContext):
#     if 'lowest' not in ctx.session:
#         ctx.session["lowest"] = None
#     # 判断处于上升趋势
#     trend = talib.LINEARREG_SLOPE(ctx.close, 5) > 0
#     # 不创新高
#     if ctx.high[-1] < ctx.high[-2]:
#         # 之前是上升趋势
#         if trend[-2] > 0 and trend[-3] > 0 and trend[-4] > 0:
#             ctx.session["lowest"] = ctx.low[-4]
#     if not ctx.session["lowest"]:
#         return
#     # 大于之前的支撑位
#     buy_signal = ctx.close[-1] > ctx.open[-1] and ctx.close[-1] > ctx.session["lowest"]
#     if not ctx.long_pos() and buy_signal:
#         ctx.buy_limit_price = ctx.close[-1]*1.095
#         ctx.buy_shares = ctx.calc_target_shares(1)
#         ctx.stop_trailing_pct = 10
#         ctx.stop_trailing_limit = ctx.buy_limit_price * 1.1
#         ctx.session['buy'] = ctx.session["lowest"]
#     # 跌破支撑位
#     if ctx.long_pos() and ctx.close[-1] < ctx.session['buy']:
#         ctx.sell_all_shares()
#     # if ctx.long_pos() and not buy_signal:
#     #     if ctx.close[-1] < ctx.session["lowest"]:
#     #         ctx.session["lowest"] = None
#     #         ctx.sell_all_shares()
#     return

def buy(ctx: ExecContext):
    mean = talib.SMA(ctx.close, 20)
    buy_signal = mean[-1] > mean[-2]
    # 判断趋势
    if not ctx.long_pos() and buy_signal:
        ctx.buy_limit_price = ctx.close[-1]*1.095
        ctx.buy_shares = ctx.calc_target_shares(1)
        # ctx.stop_trailing_pct = 5
        # ctx.stop_trailing_limit = ctx.buy_limit_price * 1.1
        ctx.stop_profit_pct = 10
        ctx.stop_loss_pct = 5
    return
start_date = '2020-01-01'
end_date = '2023-01-01'


def strategy1():
    akshare = AKShare()

    # 创建策略，设置初始资金为500000
    config = StrategyConfig(
        initial_cash=500_000, fee_mode=FeeMode.ORDER_PERCENT, fee_amount=0.005, subtract_fees=True)
    #  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
    strategy = Strategy(data_source=akshare, start_date=start_date,
                        end_date=end_date, config=config)
    #  添加执行函数，应用股票代码
    strategy.add_execution(buy, ['002594.SZ'], indicators=[])
    # 进行回测，前 20 天作为预热期
    result = strategy.backtest(warmup=100)
    # print(result.trades)
    print(result.metrics_df)
    cash_value = result.portfolio.equity/result.portfolio.equity.iloc[0]
    cash_value.plot(
        label=f"strategy")
    # chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
    # chart.plot(result.portfolio.index, result.portfolio['market_value'])
    # 绘制买入卖出点
    # 绘制买点
    buy_points = result.orders[result.orders['type'] == 'buy']
    buy_points.set_index('date', inplace=True)
    merged_df = pd.merge(buy_points, result.portfolio,
                         left_index=True, right_on='date', how='inner')
    if merged_df.shape[0] > 0:
        plt.scatter(merged_df.index, merged_df['equity'] /
                    result.portfolio.equity.iloc[0], color='g', label='buy')

    # 绘制卖点
    sell_points = result.orders[result.orders['type'] == 'sell']
    sell_points.set_index('date', inplace=True)
    merged_df = pd.merge(sell_points, result.portfolio,
                         left_index=True, right_on='date', how='inner')
    if merged_df.shape[0] > 0:
        plt.scatter(merged_df.index, merged_df['equity'] /
                    result.portfolio.equity.iloc[0], color='r', label='sell', marker='x')


def plot_symbol(symbol="000300"):
    # 沪深300作为参考
    index_zh_a_hist_df = ak.index_zh_a_hist(
        symbol=symbol, period="daily", start_date=start_date, end_date=end_date)
    index_zh_a_hist_df["日期"] = pd.to_datetime(index_zh_a_hist_df["日期"])
    index_zh_a_hist_df.set_index("日期", inplace=True)

    index_close = index_zh_a_hist_df["收盘"]
    index_time_scale = index_close/index_close.iloc[0]
    index_time_scale.plot(figsize=(18, 4), label=symbol)


# 40%
strategy1()
plot_symbol()
plot_symbol('002594')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.legend()
plt.show()
