import pandas as pd
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext, FeeMode
import matplotlib.pyplot as plt
from pybroker.vect import cross
import pybroker.indicator
import talib
import pandas_ta as ta
import akshare as ak
import numpy as np
# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

def identify_market_state(ctx: ExecContext, lookback=60):
    """识别市场状态：牛市、熊市、震荡市"""
    if len(ctx.close) < lookback:
        return 'neutral'

    # 计算长期趋势
    sma_long = talib.SMA(ctx.close, lookback)
    sma_short = talib.SMA(ctx.close, lookback//3)

    # 计算价格相对位置
    current_price = ctx.close[-1]
    price_vs_long_ma = (current_price - sma_long[-1]) / sma_long[-1]

    # 计算趋势强度
    trend_strength = (sma_short[-1] - sma_long[-1]) / sma_long[-1]

    # 计算波动率
    returns = np.diff(ctx.close[-lookback:]) / ctx.close[-lookback:-1]
    volatility = np.std(returns) * np.sqrt(252)  # 年化波动率

    # 市场状态判断
    if trend_strength > 0.02 and price_vs_long_ma > 0.05:
        return 'bull'  # 牛市
    elif trend_strength < -0.02 and price_vs_long_ma < -0.05:
        return 'bear'  # 熊市
    else:
        return 'neutral'  # 震荡市

def calculate_position_size(ctx: ExecContext, market_state, volatility):
    """根据市场状态和波动率计算仓位大小"""
    base_position = 0.3  # 基础仓位30%

    if market_state == 'bull':
        position_multiplier = 1.5  # 牛市增加仓位
    elif market_state == 'bear':
        position_multiplier = 0.5  # 熊市减少仓位
    else:
        position_multiplier = 1.0  # 震荡市正常仓位

    # 根据波动率调整
    if volatility > 0.3:  # 高波动
        volatility_multiplier = 0.7
    elif volatility < 0.15:  # 低波动
        volatility_multiplier = 1.2
    else:
        volatility_multiplier = 1.0

    final_position = base_position * position_multiplier * volatility_multiplier
    return min(final_position, 0.8)  # 最大仓位80%

def get_dynamic_stops(market_state, volatility):
    """根据市场状态动态设置止损止盈"""
    if market_state == 'bull':
        stop_loss = max(3, min(8, volatility * 20))  # 牛市较小止损
        stop_profit = max(15, min(25, volatility * 50))  # 牛市较大止盈
    elif market_state == 'bear':
        stop_loss = max(5, min(12, volatility * 25))  # 熊市较大止损
        stop_profit = max(8, min(15, volatility * 30))  # 熊市较小止盈
    else:
        stop_loss = max(4, min(10, volatility * 22))  # 震荡市中等止损
        stop_profit = max(10, min(20, volatility * 40))  # 震荡市中等止盈

    return stop_loss, stop_profit

# 改进的买卖策略
def advanced_buy_sell_strategy(ctx: ExecContext):
    """改进的多指标综合买卖策略"""
    if len(ctx.close) < 100:  # 需要足够的历史数据
        return

    # 1. 识别市场状态
    market_state = identify_market_state(ctx)

    # 2. 计算技术指标
    # 趋势指标
    sma_5 = talib.SMA(ctx.close, 5)
    sma_20 = talib.SMA(ctx.close, 20)
    sma_60 = talib.SMA(ctx.close, 60)

    # 动量指标
    rsi = talib.RSI(ctx.close, 14)
    macd, macd_signal, macd_hist = talib.MACD(ctx.close)

    # 波动率指标
    bb_upper, bb_middle, bb_lower = talib.BBANDS(ctx.close, 20, 2, 2)

    # 成交量指标
    volume_sma = talib.SMA(ctx.volume, 20)

    # 3. 计算当前波动率
    returns = np.diff(ctx.close[-60:]) / ctx.close[-60:-1]
    volatility = np.std(returns) * np.sqrt(252)

    # 4. 买入条件
    if not ctx.long_pos():
        # 基础趋势条件
        trend_up = sma_5[-1] > sma_20[-1] > sma_60[-1]
        price_above_ma = ctx.close[-1] > sma_20[-1]

        # 动量条件
        rsi_condition = 30 < rsi[-1] < 70  # RSI不在极端区域
        macd_bullish = macd[-1] > macd_signal[-1] and macd_hist[-1] > macd_hist[-2]

        # 成交量条件
        volume_surge = ctx.volume[-1] > volume_sma[-1] * 1.2

        # 布林带条件
        bb_condition = ctx.close[-1] > bb_lower[-1] and ctx.close[-1] < bb_upper[-1]

        # 根据市场状态调整买入条件
        if market_state == 'bull':
            # 牛市：相对宽松的条件
            buy_signal = trend_up and (rsi_condition or macd_bullish) and volume_surge
        elif market_state == 'bear':
            # 熊市：严格的条件
            buy_signal = trend_up and rsi_condition and macd_bullish and volume_surge and bb_condition
        else:
            # 震荡市：中等条件
            buy_signal = price_above_ma and rsi_condition and macd_bullish and volume_surge

        if buy_signal:
            # 计算仓位大小
            position_size = calculate_position_size(ctx, market_state, volatility)
            ctx.buy_shares = ctx.calc_target_shares(position_size)

            # 动态止损止盈
            stop_loss, stop_profit = get_dynamic_stops(market_state, volatility)
            ctx.stop_loss_pct = stop_loss
            ctx.stop_profit_pct = stop_profit

            # 设置买入价格（稍微高于当前价格以确保成交）
            ctx.buy_limit_price = ctx.close[-1] * 1.01

    # 5. 卖出条件
    elif ctx.long_pos():
        # 趋势转弱信号
        trend_weak = sma_5[-1] < sma_20[-1] or ctx.close[-1] < sma_20[-1]

        # 动量转弱
        rsi_overbought = rsi[-1] > 75
        macd_bearish = macd[-1] < macd_signal[-1] and macd_hist[-1] < macd_hist[-2]

        # 布林带超买
        bb_overbought = ctx.close[-1] > bb_upper[-1]

        # 根据市场状态调整卖出条件
        if market_state == 'bull':
            # 牛市：不轻易卖出
            sell_signal = (rsi_overbought and macd_bearish) or bb_overbought
        elif market_state == 'bear':
            # 熊市：快速止损
            sell_signal = trend_weak or rsi_overbought or macd_bearish
        else:
            # 震荡市：平衡策略
            sell_signal = trend_weak or (rsi_overbought and macd_bearish)

        if sell_signal:
            ctx.sell_all_shares()

    return
start_date = '2020-01-01'
end_date = '2023-01-01'


# 原始简单策略（用于对比）
def simple_buy_strategy(ctx: ExecContext):
    """原始的简单SMA策略"""
    mean = talib.SMA(ctx.close, 20)
    buy_signal = mean[-1] > mean[-2]
    if not ctx.long_pos() and buy_signal:
        ctx.buy_limit_price = ctx.close[-1]*1.095
        ctx.buy_shares = ctx.calc_target_shares(1)
        ctx.stop_profit_pct = 10
        ctx.stop_loss_pct = 5
    return

def run_strategy(strategy_func, strategy_name):
    """运行策略并返回结果"""
    akshare = AKShare()

    # 创建策略，设置初始资金为500000
    config = StrategyConfig(
        initial_cash=500_000,
        fee_mode=FeeMode.ORDER_PERCENT,
        fee_amount=0.005,
        subtract_fees=True
    )

    # 创建策略实例
    strategy = Strategy(
        data_source=akshare,
        start_date=start_date,
        end_date=end_date,
        config=config
    )

    # 添加执行函数
    strategy.add_execution(strategy_func, ['002594.SZ'], indicators=[])

    # 进行回测
    result = strategy.backtest(warmup=100)

    print(f"\n=== {strategy_name} 策略结果 ===")
    print(result.metrics_df)

    # 绘制收益曲线
    cash_value = result.portfolio.equity / result.portfolio.equity.iloc[0]
    cash_value.plot(label=strategy_name, linewidth=2)

    # 绘制买入点
    buy_points = result.orders[result.orders['type'] == 'buy']
    if not buy_points.empty:
        buy_points.set_index('date', inplace=True)
        merged_df = pd.merge(buy_points, result.portfolio,
                           left_index=True, right_on='date', how='inner')
        if merged_df.shape[0] > 0:
            plt.scatter(merged_df.index, merged_df['equity'] /
                       result.portfolio.equity.iloc[0],
                       color='g', label=f'{strategy_name} 买入', alpha=0.7, s=30)

    # 绘制卖出点
    sell_points = result.orders[result.orders['type'] == 'sell']
    if not sell_points.empty:
        sell_points.set_index('date', inplace=True)
        merged_df = pd.merge(sell_points, result.portfolio,
                           left_index=True, right_on='date', how='inner')
        if merged_df.shape[0] > 0:
            plt.scatter(merged_df.index, merged_df['equity'] /
                       result.portfolio.equity.iloc[0],
                       color='r', label=f'{strategy_name} 卖出', marker='x', alpha=0.7, s=30)

    return result

def strategy_comparison():
    """策略对比分析"""
    print("开始策略对比分析...")

    # 运行原始策略
    print("运行原始SMA策略...")
    result_simple = run_strategy(simple_buy_strategy, "原始SMA策略")

    # 运行改进策略
    print("运行改进多指标策略...")
    result_advanced = run_strategy(advanced_buy_sell_strategy, "改进多指标策略")

    return result_simple, result_advanced


def plot_symbol(symbol="000300"):
    # 沪深300作为参考
    index_zh_a_hist_df = ak.index_zh_a_hist(
        symbol=symbol, period="daily", start_date=start_date, end_date=end_date)
    index_zh_a_hist_df["日期"] = pd.to_datetime(index_zh_a_hist_df["日期"])
    index_zh_a_hist_df.set_index("日期", inplace=True)

    index_close = index_zh_a_hist_df["收盘"]
    index_time_scale = index_close/index_close.iloc[0]
    index_time_scale.plot(figsize=(18, 4), label=symbol)


if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 创建图形
    plt.figure(figsize=(15, 10))

    # 运行策略对比
    result_simple, result_advanced = strategy_comparison()

    # 绘制基准指数
    plot_symbol('000300')  # 沪深300
    plot_symbol('002594')  # 个股

    plt.title('策略收益对比分析', fontsize=16)
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('收益率', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()

    # 打印详细对比分析
    print("\n" + "="*60)
    print("策略对比总结")
    print("="*60)

    simple_metrics = result_simple.metrics_df
    advanced_metrics = result_advanced.metrics_df

    print(f"原始策略总收益率: {simple_metrics.loc['Total Return %', 'Value']:.2f}%")
    print(f"改进策略总收益率: {advanced_metrics.loc['Total Return %', 'Value']:.2f}%")

    print(f"原始策略最大回撤: {simple_metrics.loc['Max Drawdown %', 'Value']:.2f}%")
    print(f"改进策略最大回撤: {advanced_metrics.loc['Max Drawdown %', 'Value']:.2f}%")

    print(f"原始策略夏普比率: {simple_metrics.loc['Sharpe', 'Value']:.3f}")
    print(f"改进策略夏普比率: {advanced_metrics.loc['Sharpe', 'Value']:.3f}")

    print(f"原始策略胜率: {simple_metrics.loc['Win Rate %', 'Value']:.2f}%")
    print(f"改进策略胜率: {advanced_metrics.loc['Win Rate %', 'Value']:.2f}%")
