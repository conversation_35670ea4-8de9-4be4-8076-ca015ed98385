import pandas as pd
from typing import Dict, Mapping
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
from pybroker import highest, lowest

import riskfolio as rp

# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

# 当前股票上下文


def buy_low(ctx: ExecContext):
    high = ctx.high[-11:-1].max()
    low = ctx.low[-11:-1].min()
    ctx.session['buy'] = False
    ctx.session['sell'] = False
    if not ctx.long_pos() and ctx.close[-1] > ctx.low[-1]*1.05 and ctx.low[-1] < low:
        ctx.buy_shares = ctx.calc_target_shares(0.5)
        ctx.session['buy'] = True
        ctx.stop_loss_pct = 8
        ctx.stop_profit_pct = 15
    if ctx.long_pos() and ctx.close[-1] < ctx.high[-1] * 0.97 and ctx.high[-1] < high:
        ctx.sell_all_shares()
        ctx.session['sell'] = True
        print('卖出')
    return


def start_of_month(ctxs: Mapping[str, ExecContext]):
    dt = tuple(ctxs.values())[0].dt
    # 月份不等时，就是开始，更新当前月份
    if dt.month != pybroker.param('current_month'):
        pybroker.param('current_month', dt.month)
        return True
    return False


pybroker.param('lookback', 100)
def calculate_returns(ctxs: Mapping[str, ExecContext], lookback: int):
    prices = {}
    for ctx in ctxs.values():
        prices[ctx.symbol] = ctx.close[-lookback:]
    df = pd.DataFrame(prices)
    # 构建收益率表，columns是股票代码
    return df.pct_change().dropna()


def optimization(ctxs: Mapping[str, ExecContext]):
    lookback = pybroker.param('lookback')
    if start_of_month(ctxs):
        Y = calculate_returns(ctxs, lookback)
        port = rp.Portfolio(returns=Y)
        # 估计模型的输入（历史估计）
        method_mu = 'hist'  # 基于历史数据估计预期收益的方法
        method_cov = 'hist'  # 基于历史数据估计协方差矩阵的方法
        port.assets_stats(method_mu=method_mu, method_cov=method_cov)
       # 选择风险度量
        rm = 'CVaR'
        # 估计最大化风险收益比的投资组合
        w = port.optimization(model='Classic', rm=rm,
                              rf=0, obj='MinRisk',
                              l=0,
                              hist=True,
                              )
        # 获取比例
        targets = {
            symbol: w.T[symbol].values[0] for symbol in ctxs.keys()
        }
        set_target_shares(ctxs, targets)
# 设置每只股票的配置
def set_target_shares(ctxs: Mapping[str, ExecContext], target_shares: Dict[str, float]):
    for symbol, ctx in ctxs.items():
        target_share = ctx.calc_target_shares(target_shares[symbol])
        pos = ctx.long_pos()
        hasBuyOrSell = ctx.session['buy'] == True or ctx.session['sell'] == True
        if hasBuyOrSell:
            print('已操作')
            return
        #  没有持仓时，直接买入，有持仓就多卖少买
        if pos is None:
            print('none')
            # ctx.buy_shares = target_share
        elif pos.shares < target_share:
            ctx.buy_shares = target_share - pos.shares
        elif pos.shares > target_share:
            ctx.sell_shares = pos.shares - target_share
    return

akshare = AKShare()


# 创建策略，设置初始资金为500000
config = StrategyConfig(initial_cash=500_000)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=akshare, start_date='1/1/2021',
                    end_date='1/1/2023',  config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(buy_low, ['000001.SZ', '002594.SZ'], indicators=[
                       highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10)])
# 设置回测结束后的函数,每个交易日执行一次
strategy.set_after_exec(optimization)
# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=pybroker.param('lookback'))
print(result.trades)
print(result.metrics_df)
chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
