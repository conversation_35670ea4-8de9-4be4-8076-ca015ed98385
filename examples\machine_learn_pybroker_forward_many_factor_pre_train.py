import datetime
import sqlite3
import pandas as pd
import pybroker
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
import akshare as ak
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.tree import DecisionTreeRegressor
from sklearn.metrics import r2_score
from sklearn.metrics import mean_absolute_error
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import GradientBoostingRegressor
import talib
import lightgbm as lgb


def func0(x):
    return x.pct_change().shift(-1)


def hold_long(ctx):
    if not ctx.long_pos():
        # 预测值大于0，则购买
        if ctx.pred[-1] > 0.:
            ctx.buy_shares = ctx.calc_target_shares(0.01)
    else:
        #  预测值小于0，则卖出
        if ctx.pred[-1] < 0.:
            ctx.sell_all_shares()
    return
# 自定义多个特征


def compute_bb(stock_data):

    SMA20_close = talib.SMA(stock_data, timeperiod=20)

    high, mid, low = talib.BBANDS(stock_data, timeperiod=20)

    x, y, z = talib.MACD(stock_data, fastperiod=12,
                         slowperiod=26, signalperiod=9)

    dongliang = talib.MOM(stock_data, timeperiod=10)

    rsi_6 = talib.RSI(stock_data, timeperiod=6)
    rsi_24 = talib.RSI(stock_data, timeperiod=24)

    up = talib.MAX(stock_data, 20)
    down = talib.MIN(stock_data, 20)

    return_30 = stock_data.pct_change(30)
    return_5 = stock_data.pct_change(5)
    return_10 = stock_data.pct_change(10)

    df = pd.concat([SMA20_close, high, low, x, y, z, dongliang, rsi_6,
                   rsi_24, up, down, return_30, return_5, return_10], axis=1)
    df.columns = ["SMA20_close", "b_high", "b_low", "MACD_x", "MACD_y", "MACD_z",
                  "dong10", "rsi_6", "rsi_24", "up", "down", "return_30", "return_5", "return_10"]

    return df


conn = sqlite3.connect(
    r'E:\workspace\python\pybroker-test\data\stock_2018_daily\stock_2018.db')
stock_daily1 = pd.read_sql(
    "select * from stock_daily where 股票代码<'003000.SZ'", con=conn)
stock_daily1["交易日期"] = pd.to_datetime(stock_daily1["交易日期"].astype(str))

stock_daily1.columns = ['index', "date", "symbol", '股票简称', "open", "high", "low", "close", "volume",
                        '成交额(千元)', '换手率(%)', '量比', '市盈率(静态)', '市盈率(TTM)', '市盈率(动态)', '市净率',
                        '市销率', '市销率(TTM)', '股息率(%)', '股息率(TTM)(%)', '总股本(万股)', '流通股本(万股)',
                        '总市值(万元)', '流通市值(万元)']
stock_daily1_d = stock_daily1.drop(["index", "股票简称"], axis=1)
stock_daily1_d["return_s1"] = stock_daily1_d.groupby(
    "symbol", group_keys=False).close.apply(func0)


# 添加多个特征
z = stock_daily1_d.groupby("symbol", group_keys=False).close.apply(compute_bb)
stock_daily1_d = stock_daily1_d.join(z)


stock_daily1_d["close-o"] = stock_daily1_d["close"]-stock_daily1_d["open"]
stock_daily1_d["high-l"] = stock_daily1_d["high"]-stock_daily1_d["low"]


xy = stock_daily1_d[stock_daily1_d.date <
                    datetime.datetime(2021, 1, 1)].iloc[:, 2:].dropna()
xy_x = xy.drop("return_s1", axis=1)
xy_y = xy["return_s1"]
x1, x2, y1, y2 = train_test_split(
    xy_x, xy_y, test_size=0.7)  # 分割数据出训练集与测试集，0.7是两者行数的比例

# 创建一个线性回归模型
# clf = LinearRegression()
# clf.fit(x1, y1)
# print(r2_score(y2,clf.predict(x2)))

# 创建一个LGBMRegressor模型，目标函数为回归，叶子节点数为31，学习率为0.1，估计器个数为30
gbm = lgb.LGBMRegressor(objective='regression',
                        num_leaves=31, learning_rate=0.1, n_estimators=30)
gbm.fit(x1, y1)
y_pred = gbm.predict(x2)
print(r2_score(y2, y_pred))

# 五个月的预测值
xy_t = stock_daily1_d[(stock_daily1_d.date > datetime.datetime(2021, 1, 1)) & (
    stock_daily1_d.date < datetime.datetime(2021, 5, 1))].iloc[:, 2:].dropna()
xy_x = xy_t.drop("return_s1", axis=1)
xy_y = xy_t["return_s1"]
y_pred = gbm.predict(xy_x)
print(r2_score(xy_y, y_pred))


# 将预测值作为一列
y_pred = pd.Series(y_pred, index=xy_x.index, name="pred")

pyb_data_pe = stock_daily1_d[(stock_daily1_d.date > datetime.datetime(2021, 1, 1)) & (
    stock_daily1_d.date < datetime.datetime(2021, 5, 1))].iloc[:, 0:7]
pyb_data_pe = pd.concat([pyb_data_pe, y_pred], axis=1)
pyb_data_pe.fillna(0, inplace=True)

pybroker.register_columns('pred')

# 抽样方法
config = StrategyConfig()
strategy = Strategy(pyb_data_pe, '2021-01-01', '2021-05-01', config)

strategy.clear_executions()
# 100支股票
strategy.add_execution(hold_long, pyb_data_pe.symbol.unique()[250:350])
result = strategy.backtest()

print(result.trades)
print(result.metrics_df)

# 沪深300作为参考
index_zh_a_hist_df = ak.index_zh_a_hist(
    symbol="000300", period="daily", start_date="2021-01-01", end_date="2021-05-01")
index_zh_a_hist_df["日期"] = pd.to_datetime(index_zh_a_hist_df["日期"])
index_zh_a_hist_df.set_index("日期", inplace=True)

index_close = index_zh_a_hist_df["收盘"]
index_time_scale = index_close/index_close.iloc[0]
index_time_scale.plot(figsize=(18, 4))

cash_value = result.portfolio.equity/result.portfolio.equity.iloc[0]
cash_value.plot()

# chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
# chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
