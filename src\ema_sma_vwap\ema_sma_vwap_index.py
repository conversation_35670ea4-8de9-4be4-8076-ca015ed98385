import pandas as pd
import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext, FeeMode
import matplotlib.pyplot as plt
from pybroker.vect import cross
import pybroker.indicator
import talib
import pandas_ta as ta
import akshare as ak
# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

# 使用talib自定义indicator


def buy(ctx: ExecContext):
    ema_short = ctx.indicator('ema_short')
    ema_long = ctx.indicator('ema_long')
    ema_exit_short = ctx.indicator('ema_exit_short')
    ema_exit_long = ctx.indicator('ema_exit_long')
    sma = ctx.indicator('sma')
    vwap = ctx.indicator('vwap')
    # 判断短期均线上穿长期均线，收盘价格高于典型价格，收盘价格高于平均价格，则买入
    buy_signal = cross(
        ema_short, ema_long)[-1] and ctx.close[-1] > vwap[-1] and ctx.close[-1] > sma[-1]
    # 判断长期均线上穿短期均线，则卖出
    sell_signal = cross(ema_exit_long, ema_exit_short)[-1]
    if not ctx.long_pos() and buy_signal:
        ctx.buy_limit_price = ctx.close[-1]*1.095
        ctx.buy_shares = ctx.calc_target_shares(0.5)
        ctx.score = ctx.volume[-1]
        print('买入', ctx.dt, buy_signal)
    if ctx.long_pos() and sell_signal:
        ctx.sell_all_shares()
        print('卖出', ctx.dt, sell_signal)

    return


def vwap(data):
    price = (data.close + data.high + data.low)/3
    vwap = (price * data.volume).cumsum() / data.volume.cumsum()
    # print(ta.vwap(data.high, data.low, data.close, data.volume))
    return vwap


start_date = '2020-01-01'
end_date = '2023-01-01'


def strategy1(ema_short=17, ema_long=31, ema_exit_short=8, ema_exit_long=9, sma=69):
    akshare = AKShare()

    # 创建策略，设置初始资金为500000
    config = StrategyConfig(
        initial_cash=500_000, fee_mode=FeeMode.ORDER_PERCENT, fee_amount=0.005, subtract_fees=True)
    #  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
    strategy = Strategy(data_source=akshare, start_date=start_date,
                        end_date=end_date, config=config)
    #  添加执行函数，应用股票代码
    strategy.add_execution(buy, ['000001.SZ', '002594.SZ'], indicators=[
        # 短期ema入场用
                           pybroker.indicator('ema_short', lambda data: talib.EMA(
                               data.close, timeperiod=ema_short)),
                           # 长期ema入场用
                           pybroker.indicator('ema_long', lambda data: talib.EMA(
                               data.close, timeperiod=ema_long)),
                           # 短期ema出场用
                           pybroker.indicator('ema_exit_short', lambda data: talib.EMA(
                               data.close, timeperiod=ema_exit_short)),
                           # 长期ema出场用
                           pybroker.indicator('ema_exit_long', lambda data: talib.EMA(
                               data.close, timeperiod=ema_exit_long)),
                           # 计算长期简单移动平均线
                           pybroker.indicator('sma', lambda data: talib.EMA(
                               data.close, timeperiod=sma)),
                           # 计算成交量加权平均价格
                           pybroker.indicator('vwap', vwap)
                           ])
    # 进行回测，前 20 天作为预热期
    result = strategy.backtest(warmup=69)
    # print(result.trades)
    print(result.metrics_df)
    cash_value = result.portfolio.equity/result.portfolio.equity.iloc[0]
    cash_value.plot(
        label=f"strategy_{ema_short}_{ema_long}_{ema_exit_short}_{ema_exit_long}_{sma}")
    # chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
    # chart.plot(result.portfolio.index, result.portfolio['market_value'])


def plot300():
    # 沪深300作为参考
    index_zh_a_hist_df = ak.index_zh_a_hist(
        symbol="000300", period="daily", start_date=start_date, end_date=end_date)
    index_zh_a_hist_df["日期"] = pd.to_datetime(index_zh_a_hist_df["日期"])
    index_zh_a_hist_df.set_index("日期", inplace=True)

    index_close = index_zh_a_hist_df["收盘"]
    index_time_scale = index_close/index_close.iloc[0]
    index_time_scale.plot(figsize=(18, 4), label="300")

# 40%
strategy1()
# 40%
strategy1(ema_short=17, ema_long=31,
          ema_exit_short=7, ema_exit_long=13, sma=67)
# 130%
strategy1(ema_short=7, ema_long=13,
          ema_exit_short=7, ema_exit_long=13, sma=67)
plot300()
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.legend()
plt.show()
