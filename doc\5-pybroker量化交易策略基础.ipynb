{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 基于Pybroker框架的量化交易策略\n", "\n", "* 量化交易策略平台：掘金，聚宽，米筐，优矿等\n", "* 常见的量化交易策略库：Zipline、Backtrader、PyBroker等\n", "\n", "## 什么是量化交易策略\n", "\n", "量化交易策略是指通过数学模型和算法，对金融市场的价格、趋势、波动等数据进行挖掘和分析，从而制定出买卖股票、期货、外汇等金融产品的交易策略。\n", "\n", "## pybroker框架\n", "\n", "pybroker是一个基于Python的量化交易策略框架，它提供了一套完整的量化交易策略开发、回测、模拟和实盘交易的功能。\n", "\n", "https://www.pybroker.com/zh-cn/latest/index.html\n", "\n", "https://github.com/edtechre/pybroker/tree/master\n", "\n", "* 库很新，学习资源少，除了官方文档，没有其他学习资料，借助大模型阅读源代码进行提问学习会有帮助\n", "* 使用简单，速度快，支持在线akshare数据源也支持离线数据，对机器学习模型支持友好\n", "\n", "## pybroker安装（本课程第一次课大部分同学已经完成安装不需要再走下面的流程）\n", "\n", "* 直接pip通常可以：pip install lib-pybroker -i https://pypi.tuna.tsinghua.edu.cn/simple\n", "* 建议先创建虚拟环境在再在虚拟环境中装库（如果直接装好能用那就不用搞虚拟环境）\n", "* 如果想要和talib结合，某些版本因为numpy版本冲突，可能需要先安装ta-lib再安装pybroker\n", "\n", "ta_lib安装需要下载对应版本的ta_lib库\n", "\n", "https://github.com/cgohlke/talib-build/releases\n", "\n", "## pybroker与akshare的使用\n", "\n", "* 由于akshare是一个爬虫接口，对应数据源通常会不稳定，一旦出现异常常用手段就是保持更新\n", "\n", "* pip install --upgrade lib-pybroker akshare -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## pybroker 的数据形式与离线保存\n", "\n", "* pybroker对数据的要求\n", "\n", "* 离线保存后，pybroker会自动读取离线数据，避免总是网络获取"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["<diskcache.core.Cache at 0x22e25d29f50>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pybroker\n", "from pybroker.ext.data import AKShare\n", "from pybroker import ExecContext, StrategyConfig, Strategy\n", "from pybroker.ext.data import AKShare\n", "import matplotlib.pyplot as plt\n", "\n", "akshare = AKShare()\n", "\n", "pybroker.enable_data_source_cache('akshare')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded cached bar data.\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-03-02</td>\n", "      <td>000001.SZ</td>\n", "      <td>3653.44</td>\n", "      <td>3739.58</td>\n", "      <td>3594.93</td>\n", "      <td>3658.31</td>\n", "      <td>1473425</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-03-03</td>\n", "      <td>000001.SZ</td>\n", "      <td>3646.94</td>\n", "      <td>3890.73</td>\n", "      <td>3627.43</td>\n", "      <td>3879.35</td>\n", "      <td>1919635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-03-04</td>\n", "      <td>000001.SZ</td>\n", "      <td>3840.34</td>\n", "      <td>3957.36</td>\n", "      <td>3820.84</td>\n", "      <td>3864.72</td>\n", "      <td>1213579</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-03-05</td>\n", "      <td>000001.SZ</td>\n", "      <td>3828.97</td>\n", "      <td>3874.48</td>\n", "      <td>3692.44</td>\n", "      <td>3770.46</td>\n", "      <td>880171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-03-08</td>\n", "      <td>000001.SZ</td>\n", "      <td>3786.71</td>\n", "      <td>3827.34</td>\n", "      <td>3630.68</td>\n", "      <td>3650.19</td>\n", "      <td>993774</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>967</th>\n", "      <td>2023-02-23</td>\n", "      <td>600000.SH</td>\n", "      <td>77.41</td>\n", "      <td>77.61</td>\n", "      <td>77.28</td>\n", "      <td>77.28</td>\n", "      <td>114365</td>\n", "    </tr>\n", "    <tr>\n", "      <th>968</th>\n", "      <td>2023-02-24</td>\n", "      <td>600000.SH</td>\n", "      <td>77.22</td>\n", "      <td>77.48</td>\n", "      <td>76.95</td>\n", "      <td>76.95</td>\n", "      <td>166811</td>\n", "    </tr>\n", "    <tr>\n", "      <th>969</th>\n", "      <td>2023-02-27</td>\n", "      <td>600000.SH</td>\n", "      <td>76.82</td>\n", "      <td>77.08</td>\n", "      <td>76.82</td>\n", "      <td>76.82</td>\n", "      <td>158006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>970</th>\n", "      <td>2023-02-28</td>\n", "      <td>600000.SH</td>\n", "      <td>76.95</td>\n", "      <td>77.08</td>\n", "      <td>76.69</td>\n", "      <td>76.95</td>\n", "      <td>174481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>971</th>\n", "      <td>2023-03-01</td>\n", "      <td>600000.SH</td>\n", "      <td>76.89</td>\n", "      <td>77.54</td>\n", "      <td>76.89</td>\n", "      <td>77.48</td>\n", "      <td>256613</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>972 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          date     symbol     open     high      low    close   volume\n", "0   2021-03-02  000001.SZ  3653.44  3739.58  3594.93  3658.31  1473425\n", "1   2021-03-03  000001.SZ  3646.94  3890.73  3627.43  3879.35  1919635\n", "2   2021-03-04  000001.SZ  3840.34  3957.36  3820.84  3864.72  1213579\n", "3   2021-03-05  000001.SZ  3828.97  3874.48  3692.44  3770.46   880171\n", "4   2021-03-08  000001.SZ  3786.71  3827.34  3630.68  3650.19   993774\n", "..         ...        ...      ...      ...      ...      ...      ...\n", "967 2023-02-23  600000.SH    77.41    77.61    77.28    77.28   114365\n", "968 2023-02-24  600000.SH    77.22    77.48    76.95    76.95   166811\n", "969 2023-02-27  600000.SH    76.82    77.08    76.82    76.82   158006\n", "970 2023-02-28  600000.SH    76.95    77.08    76.69    76.95   174481\n", "971 2023-03-01  600000.SH    76.89    77.54    76.89    77.48   256613\n", "\n", "[972 rows x 7 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取股票数据\n", "# 你可以用000001替换000001.SZ，程序仍然可以正常运行！\n", "# 并且你可以将起始日期设置为“20210301”这种格式。\n", "# 你还可以将“adjust”设置为“qfq”（前复权）或“hfq”（后复权）来调整数据，\n", "# 并将“timeframe”设置为“1d”（日数据）、“1w”（周数据）以获取每日、每周的数据。 \n", "df = akshare.query(\n", "    symbols=['000001.SZ', '600000.SH'],\n", "    start_date='3/2/2021',\n", "    end_date='3/1/2023',\n", "    adjust=\"hfq\",\n", "    timeframe=\"1d\",\n", ")\n", "df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded cached bar data.\n", "\n"]}], "source": ["df = akshare.query(symbols='000001', start_date='20200101', end_date='20230830', adjust='')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-01-02</td>\n", "      <td>000001</td>\n", "      <td>16.65</td>\n", "      <td>16.95</td>\n", "      <td>16.55</td>\n", "      <td>16.87</td>\n", "      <td>1530232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-01-03</td>\n", "      <td>000001</td>\n", "      <td>16.94</td>\n", "      <td>17.31</td>\n", "      <td>16.92</td>\n", "      <td>17.18</td>\n", "      <td>1116195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-01-06</td>\n", "      <td>000001</td>\n", "      <td>17.01</td>\n", "      <td>17.34</td>\n", "      <td>16.91</td>\n", "      <td>17.07</td>\n", "      <td>862084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-01-07</td>\n", "      <td>000001</td>\n", "      <td>17.13</td>\n", "      <td>17.28</td>\n", "      <td>16.95</td>\n", "      <td>17.15</td>\n", "      <td>728608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-01-08</td>\n", "      <td>000001</td>\n", "      <td>17.00</td>\n", "      <td>17.05</td>\n", "      <td>16.63</td>\n", "      <td>16.66</td>\n", "      <td>847824</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>2023-08-24</td>\n", "      <td>000001</td>\n", "      <td>11.29</td>\n", "      <td>11.32</td>\n", "      <td>11.05</td>\n", "      <td>11.13</td>\n", "      <td>1291271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>2023-08-25</td>\n", "      <td>000001</td>\n", "      <td>11.10</td>\n", "      <td>11.33</td>\n", "      <td>11.08</td>\n", "      <td>11.23</td>\n", "      <td>845350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>2023-08-28</td>\n", "      <td>000001</td>\n", "      <td>11.84</td>\n", "      <td>11.93</td>\n", "      <td>11.49</td>\n", "      <td>11.52</td>\n", "      <td>1997610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>2023-08-29</td>\n", "      <td>000001</td>\n", "      <td>11.47</td>\n", "      <td>11.49</td>\n", "      <td>11.13</td>\n", "      <td>11.31</td>\n", "      <td>1964961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>2023-08-30</td>\n", "      <td>000001</td>\n", "      <td>11.27</td>\n", "      <td>11.27</td>\n", "      <td>11.13</td>\n", "      <td>11.13</td>\n", "      <td>1375638</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>889 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          date  symbol   open   high    low  close   volume\n", "0   2020-01-02  000001  16.65  16.95  16.55  16.87  1530232\n", "1   2020-01-03  000001  16.94  17.31  16.92  17.18  1116195\n", "2   2020-01-06  000001  17.01  17.34  16.91  17.07   862084\n", "3   2020-01-07  000001  17.13  17.28  16.95  17.15   728608\n", "4   2020-01-08  000001  17.00  17.05  16.63  16.66   847824\n", "..         ...     ...    ...    ...    ...    ...      ...\n", "884 2023-08-24  000001  11.29  11.32  11.05  11.13  1291271\n", "885 2023-08-25  000001  11.10  11.33  11.08  11.23   845350\n", "886 2023-08-28  000001  11.84  11.93  11.49  11.52  1997610\n", "887 2023-08-29  000001  11.47  11.49  11.13  11.31  1964961\n", "888 2023-08-30  000001  11.27  11.27  11.13  11.13  1375638\n", "\n", "[889 rows x 7 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第一个量化策略\n", "\n", "* 数据的导入\n", "\n", "* 策略函数的编写与运行机制：每天每个股票都会运行一次\n", "\n", "* 策略的结果分析"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2023-08-01 00:00:00 to 2023-08-30 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2023-08-01 00:00:00 to 2023-08-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 22) |                         | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "100% (22 of 22) |########################| Elapsed Time: 0:00:00 ETA:  00:00:00\n", "100% (22 of 22) |########################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["None\n", "None\n", "Position(symbol='000001', shares=Decimal('20559'), type='long', close=Decimal('12.03'), equity=Decimal('247324.77'), market_value=Decimal('247324.77'), margin=Decimal('0'), pnl=Decimal('-1027.95'), entries=deque([Entry(id=1, date=numpy.datetime64('2023-08-02T00:00:00.000000000'), symbol='000001', shares=Decimal('20559'), price=Decimal('12.08'), type='long', bars=0, stops=[Stop(id=145, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.15'), mfe=Decimal('0.16'))]), bars=0)\n", "Position(symbol='600000', shares=Decimal('32894'), type='long', close=Decimal('7.48'), equity=Decimal('246047.12'), market_value=Decimal('246047.12'), margin=Decimal('0'), pnl=Decimal('-986.82'), entries=deque([Entry(id=2, date=numpy.datetime64('2023-08-02T00:00:00.000000000'), symbol='600000', shares=Decimal('32894'), price=Decimal('7.51'), type='long', bars=0, stops=[Stop(id=146, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.08'), mfe=Decimal('0.08'))]), bars=0)\n", "Position(symbol='000001', shares=Decimal('20559'), type='long', close=Decimal('12.33'), equity=Decimal('253492.47'), market_value=Decimal('253492.47'), margin=Decimal('0'), pnl=Decimal('5139.75'), entries=deque([Entry(id=1, date=numpy.datetime64('2023-08-02T00:00:00.000000000'), symbol='000001', shares=Decimal('20559'), price=Decimal('12.08'), type='long', bars=1, stops=[Stop(id=145, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.15'), mfe=Decimal('0.29'))]), bars=1)\n", "Position(symbol='600000', shares=Decimal('32894'), type='long', close=Decimal('7.56'), equity=Decimal('248678.64'), market_value=Decimal('248678.64'), margin=Decimal('0'), pnl=Decimal('1644.70'), entries=deque([Entry(id=2, date=numpy.datetime64('2023-08-02T00:00:00.000000000'), symbol='600000', shares=Decimal('32894'), price=Decimal('7.51'), type='long', bars=1, stops=[Stop(id=146, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.08'), mfe=Decimal('0.08'))]), bars=1)\n", "Position(symbol='000001', shares=Decimal('20559'), type='long', close=Decimal('12.3'), equity=Decimal('252875.7'), market_value=Decimal('252875.7'), margin=Decimal('0'), pnl=Decimal('4522.98'), entries=deque([Entry(id=1, date=numpy.datetime64('2023-08-02T00:00:00.000000000'), symbol='000001', shares=Decimal('20559'), price=Decimal('12.08'), type='long', bars=2, stops=[Stop(id=145, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.15'), mfe=Decimal('0.45'))]), bars=2)\n", "Position(symbol='600000', shares=Decimal('32894'), type='long', close=Decimal('7.58'), equity=Decimal('249336.52'), market_value=Decimal('249336.52'), margin=Decimal('0'), pnl=Decimal('2302.58'), entries=deque([Entry(id=2, date=numpy.datetime64('2023-08-02T00:00:00.000000000'), symbol='600000', shares=Decimal('32894'), price=Decimal('7.51'), type='long', bars=2, stops=[Stop(id=146, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.08'), mfe=Decimal('0.14'))]), bars=2)\n", "None\n", "None\n", "Position(symbol='000001', shares=Decimal('20670'), type='long', close=Decimal('12.1'), equity=Decimal('250107.0'), market_value=Decimal('250107.0'), margin=Decimal('0'), pnl=Decimal('0.0'), entries=deque([Entry(id=3, date=numpy.datetime64('2023-08-08T00:00:00.000000000'), symbol='000001', shares=Decimal('20670'), price=Decimal('12.1'), type='long', bars=0, stops=[Stop(id=147, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.1'), mfe=Decimal('0.11'))]), bars=0)\n", "Position(symbol='600000', shares=Decimal('33425'), type='long', close=Decimal('7.52'), equity=Decimal('251356.00'), market_value=Decimal('251356.00'), margin=Decimal('0'), pnl=Decimal('1002.75'), entries=deque([Entry(id=4, date=numpy.datetime64('2023-08-08T00:00:00.000000000'), symbol='600000', shares=Decimal('33425'), price=Decimal('7.49'), type='long', bars=0, stops=[Stop(id=148, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.06'), mfe=Decimal('0.06'))]), bars=0)\n", "Position(symbol='000001', shares=Decimal('20670'), type='long', close=Decimal('12.17'), equity=Decimal('251553.90'), market_value=Decimal('251553.90'), margin=Decimal('0'), pnl=Decimal('1446.90'), entries=deque([Entry(id=3, date=numpy.datetime64('2023-08-08T00:00:00.000000000'), symbol='000001', shares=Decimal('20670'), price=Decimal('12.1'), type='long', bars=1, stops=[Stop(id=147, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.1'), mfe=Decimal('0.11'))]), bars=1)\n", "Position(symbol='600000', shares=Decimal('33425'), type='long', close=Decimal('7.55'), equity=Decimal('252358.75'), market_value=Decimal('252358.75'), margin=Decimal('0'), pnl=Decimal('2005.50'), entries=deque([Entry(id=4, date=numpy.datetime64('2023-08-08T00:00:00.000000000'), symbol='600000', shares=Decimal('33425'), price=Decimal('7.49'), type='long', bars=1, stops=[Stop(id=148, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.06'), mfe=Decimal('0.06'))]), bars=1)\n", "Position(symbol='000001', shares=Decimal('20670'), type='long', close=Decimal('12.16'), equity=Decimal('251347.20'), market_value=Decimal('251347.20'), margin=Decimal('0'), pnl=Decimal('1240.20'), entries=deque([Entry(id=3, date=numpy.datetime64('2023-08-08T00:00:00.000000000'), symbol='000001', shares=Decimal('20670'), price=Decimal('12.1'), type='long', bars=2, stops=[Stop(id=147, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.1'), mfe=Decimal('0.11'))]), bars=2)\n", "Position(symbol='600000', shares=Decimal('33425'), type='long', close=Decimal('7.42'), equity=Decimal('248013.50'), market_value=Decimal('248013.50'), margin=Decimal('0'), pnl=Decimal('-2339.75'), entries=deque([Entry(id=4, date=numpy.datetime64('2023-08-08T00:00:00.000000000'), symbol='600000', shares=Decimal('33425'), price=Decimal('7.49'), type='long', bars=2, stops=[Stop(id=148, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.14'), mfe=Decimal('0.06'))]), bars=2)\n", "None\n", "None\n", "Position(symbol='000001', shares=Decimal('20911'), type='long', close=Decimal('11.67'), equity=Decimal('244031.37'), market_value=Decimal('244031.37'), margin=Decimal('0'), pnl=Decimal('-209.11'), entries=deque([Entry(id=5, date=numpy.datetime64('2023-08-14T00:00:00.000000000'), symbol='000001', shares=Decimal('20911'), price=Decimal('11.68'), type='long', bars=0, stops=[Stop(id=149, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.09'), mfe=Decimal('0.10'))]), bars=0)\n", "Position(symbol='600000', shares=Decimal('34390'), type='long', close=Decimal('7.1'), equity=Decimal('244169.0'), market_value=Decimal('244169.0'), margin=Decimal('0'), pnl=Decimal('-687.80'), entries=deque([Entry(id=6, date=numpy.datetime64('2023-08-14T00:00:00.000000000'), symbol='600000', shares=Decimal('34390'), price=Decimal('7.12'), type='long', bars=0, stops=[Stop(id=150, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.07'), mfe=Decimal('0.08'))]), bars=0)\n", "Position(symbol='000001', shares=Decimal('20911'), type='long', close=Decimal('11.73'), equity=Decimal('245286.03'), market_value=Decimal('245286.03'), margin=Decimal('0'), pnl=Decimal('1045.55'), entries=deque([Entry(id=5, date=numpy.datetime64('2023-08-14T00:00:00.000000000'), symbol='000001', shares=Decimal('20911'), price=Decimal('11.68'), type='long', bars=1, stops=[Stop(id=149, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.09'), mfe=Decimal('0.12'))]), bars=1)\n", "Position(symbol='600000', shares=Decimal('34390'), type='long', close=Decimal('7.17'), equity=Decimal('246576.30'), market_value=Decimal('246576.30'), margin=Decimal('0'), pnl=Decimal('1719.50'), entries=deque([Entry(id=6, date=numpy.datetime64('2023-08-14T00:00:00.000000000'), symbol='600000', shares=Decimal('34390'), price=Decimal('7.12'), type='long', bars=1, stops=[Stop(id=150, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.07'), mfe=Decimal('0.08'))]), bars=1)\n", "Position(symbol='000001', shares=Decimal('20911'), type='long', close=Decimal('11.73'), equity=Decimal('245286.03'), market_value=Decimal('245286.03'), margin=Decimal('0'), pnl=Decimal('1045.55'), entries=deque([Entry(id=5, date=numpy.datetime64('2023-08-14T00:00:00.000000000'), symbol='000001', shares=Decimal('20911'), price=Decimal('11.68'), type='long', bars=2, stops=[Stop(id=149, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.09'), mfe=Decimal('0.17'))]), bars=2)\n", "Position(symbol='600000', shares=Decimal('34390'), type='long', close=Decimal('7.14'), equity=Decimal('245544.60'), market_value=Decimal('245544.60'), margin=Decimal('0'), pnl=Decimal('687.80'), entries=deque([Entry(id=6, date=numpy.datetime64('2023-08-14T00:00:00.000000000'), symbol='600000', shares=Decimal('34390'), price=Decimal('7.12'), type='long', bars=2, stops=[Stop(id=150, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.07'), mfe=Decimal('0.08'))]), bars=2)\n", "None\n", "None\n", "Position(symbol='000001', shares=Decimal('21370'), type='long', close=Decimal('11.56'), equity=Decimal('247037.20'), market_value=Decimal('247037.20'), margin=Decimal('0'), pnl=Decimal('-1709.60'), entries=deque([Entry(id=7, date=numpy.datetime64('2023-08-18T00:00:00.000000000'), symbol='000001', shares=Decimal('21370'), price=Decimal('11.64'), type='long', bars=0, stops=[Stop(id=151, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.11'), mfe=Decimal('0.10'))]), bars=0)\n", "Position(symbol='600000', shares=Decimal('34516'), type='long', close=Decimal('7.07'), equity=Decimal('244028.12'), market_value=Decimal('244028.12'), margin=Decimal('0'), pnl=Decimal('-1725.80'), entries=deque([Entry(id=8, date=numpy.datetime64('2023-08-18T00:00:00.000000000'), symbol='600000', shares=Decimal('34516'), price=Decimal('7.12'), type='long', bars=0, stops=[Stop(id=152, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.05'), mfe=Decimal('0.05'))]), bars=0)\n", "Position(symbol='000001', shares=Decimal('21370'), type='long', close=Decimal('11.32'), equity=Decimal('241908.40'), market_value=Decimal('241908.40'), margin=Decimal('0'), pnl=Decimal('-6838.40'), entries=deque([Entry(id=7, date=numpy.datetime64('2023-08-18T00:00:00.000000000'), symbol='000001', shares=Decimal('21370'), price=Decimal('11.64'), type='long', bars=1, stops=[Stop(id=151, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.32'), mfe=Decimal('0.10'))]), bars=1)\n", "Position(symbol='600000', shares=Decimal('34516'), type='long', close=Decimal('6.96'), equity=Decimal('240231.36'), market_value=Decimal('240231.36'), margin=Decimal('0'), pnl=Decimal('-5522.56'), entries=deque([Entry(id=8, date=numpy.datetime64('2023-08-18T00:00:00.000000000'), symbol='600000', shares=Decimal('34516'), price=Decimal('7.12'), type='long', bars=1, stops=[Stop(id=152, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.16'), mfe=Decimal('0.05'))]), bars=1)\n", "Position(symbol='000001', shares=Decimal('21370'), type='long', close=Decimal('11.37'), equity=Decimal('242976.90'), market_value=Decimal('242976.90'), margin=Decimal('0'), pnl=Decimal('-5769.90'), entries=deque([Entry(id=7, date=numpy.datetime64('2023-08-18T00:00:00.000000000'), symbol='000001', shares=Decimal('21370'), price=Decimal('11.64'), type='long', bars=2, stops=[Stop(id=151, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.33'), mfe=Decimal('0.10'))]), bars=2)\n", "Position(symbol='600000', shares=Decimal('34516'), type='long', close=Decimal('7.01'), equity=Decimal('241957.16'), market_value=Decimal('241957.16'), margin=Decimal('0'), pnl=Decimal('-3796.76'), entries=deque([Entry(id=8, date=numpy.datetime64('2023-08-18T00:00:00.000000000'), symbol='600000', shares=Decimal('34516'), price=Decimal('7.12'), type='long', bars=2, stops=[Stop(id=152, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.16'), mfe=Decimal('0.05'))]), bars=2)\n", "None\n", "None\n", "Position(symbol='000001', shares=Decimal('21560'), type='long', close=Decimal('11.13'), equity=Decimal('239962.80'), market_value=Decimal('239962.80'), margin=Decimal('0'), pnl=Decimal('-1078.00'), entries=deque([Entry(id=9, date=numpy.datetime64('2023-08-24T00:00:00.000000000'), symbol='000001', shares=Decimal('21560'), price=Decimal('11.18'), type='long', bars=0, stops=[Stop(id=153, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.13'), mfe=Decimal('0.14'))]), bars=0)\n", "Position(symbol='600000', shares=Decimal('34552'), type='long', close=Decimal('6.99'), equity=Decimal('241518.48'), market_value=Decimal('241518.48'), margin=Decimal('0'), pnl=Decimal('-1036.56'), entries=deque([Entry(id=10, date=numpy.datetime64('2023-08-24T00:00:00.000000000'), symbol='600000', shares=Decimal('34552'), price=Decimal('7.02'), type='long', bars=0, stops=[Stop(id=154, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.04'), mfe=Decimal('0.04'))]), bars=0)\n", "Position(symbol='000001', shares=Decimal('21560'), type='long', close=Decimal('11.23'), equity=Decimal('242118.80'), market_value=Decimal('242118.80'), margin=Decimal('0'), pnl=Decimal('1078.00'), entries=deque([Entry(id=9, date=numpy.datetime64('2023-08-24T00:00:00.000000000'), symbol='000001', shares=Decimal('21560'), price=Decimal('11.18'), type='long', bars=1, stops=[Stop(id=153, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.13'), mfe=Decimal('0.15'))]), bars=1)\n", "Position(symbol='600000', shares=Decimal('34552'), type='long', close=Decimal('7.05'), equity=Decimal('243591.60'), market_value=Decimal('243591.60'), margin=Decimal('0'), pnl=Decimal('1036.56'), entries=deque([Entry(id=10, date=numpy.datetime64('2023-08-24T00:00:00.000000000'), symbol='600000', shares=Decimal('34552'), price=Decimal('7.02'), type='long', bars=1, stops=[Stop(id=154, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.05'), mfe=Decimal('0.07'))]), bars=1)\n", "Position(symbol='000001', shares=Decimal('21560'), type='long', close=Decimal('11.52'), equity=Decimal('248371.20'), market_value=Decimal('248371.20'), margin=Decimal('0'), pnl=Decimal('7330.40'), entries=deque([Entry(id=9, date=numpy.datetime64('2023-08-24T00:00:00.000000000'), symbol='000001', shares=Decimal('21560'), price=Decimal('11.18'), type='long', bars=2, stops=[Stop(id=153, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.13'), mfe=Decimal('0.75'))]), bars=2)\n", "Position(symbol='600000', shares=Decimal('34552'), type='long', close=Decimal('7.15'), equity=Decimal('247046.80'), market_value=Decimal('247046.80'), margin=Decimal('0'), pnl=Decimal('4491.76'), entries=deque([Entry(id=10, date=numpy.datetime64('2023-08-24T00:00:00.000000000'), symbol='600000', shares=Decimal('34552'), price=Decimal('7.02'), type='long', bars=2, stops=[Stop(id=154, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.05'), mfe=Decimal('0.30'))]), bars=2)\n", "None\n", "None\n", "Position(symbol='000001', shares=Decimal('21723'), type='long', close=Decimal('11.13'), equity=Decimal('241776.99'), market_value=Decimal('241776.99'), margin=Decimal('0'), pnl=Decimal('-1520.61'), entries=deque([Entry(id=11, date=numpy.datetime64('2023-08-30T00:00:00.000000000'), symbol='000001', shares=Decimal('21723'), price=Decimal('11.2'), type='long', bars=0, stops=[Stop(id=155, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.07'), mfe=Decimal('0.07'))]), bars=0)\n", "Position(symbol='600000', shares=Decimal('34751'), type='long', close=Decimal('7.03'), equity=Decimal('244299.53'), market_value=Decimal('244299.53'), margin=Decimal('0'), pnl=Decimal('-1042.53'), entries=deque([Entry(id=12, date=numpy.datetime64('2023-08-30T00:00:00.000000000'), symbol='600000', shares=Decimal('34751'), price=Decimal('7.06'), type='long', bars=0, stops=[Stop(id=156, symbol='600000', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.05'), mfe=Decimal('0.04'))]), bars=0)\n", "\n", "Finished backtest: 0:00:00\n"]}], "source": ["\n", "# 定义交易策略函数\n", "def buy_func(ctx: ExecContext) -> None:\n", "    pos = ctx.long_pos()  # 获取当前的长期持有的股票\n", "    print(pos)\n", "    #print(ctx.date[-1])\n", "    #print(ctx.volume)\n", "    #print(ctx.symbol)\n", "    if pos:  # 如果当前持有股票\n", "        #ctx.sell_shares = pos.shares  # 卖出所有的股票\n", "        pass\n", "    else:  # 如果当前没有持有股票\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)  # 买入全部可购买的股票\n", "        ctx.hold_bars = 3  # 设置持有的交易日为3天\n", "\n", "\n", "# 创建策略配置对象，设置初始现金为 500,000 元\n", "my_config = StrategyConfig(initial_cash=500000)\n", "\n", "# 创建策略对象，设置数据源为 AKShare，开始日期为 '20230801'，结束日期为 '20230830'，策略配置为 my_config\n", "strategy = Strategy(data_source=AKShare(), start_date='20230801', end_date='20230830', config=my_config)\n", "\n", "# 将定义的交易策略函数添加到策略对象中，应用于股票 '000001'\n", "strategy.add_execution(fn=buy_func, symbols=['000001',\"600000\"])\n", "\n", "# 执行回测\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='date'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result.portfolio.equity.plot()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x22e4080e550>]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "chart = plt.subplot2grid((5, 2), (0, 0), rowspan=3, colspan=2)\n", "plt.xticks(rotation=30)\n", "chart.plot(result.portfolio.index, result.portfolio['market_value'])\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001</td>\n", "      <td>2023-08-02</td>\n", "      <td>20559</td>\n", "      <td>NaN</td>\n", "      <td>12.08</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>600000</td>\n", "      <td>2023-08-02</td>\n", "      <td>32894</td>\n", "      <td>NaN</td>\n", "      <td>7.51</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>000001</td>\n", "      <td>2023-08-07</td>\n", "      <td>20559</td>\n", "      <td>NaN</td>\n", "      <td>12.18</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>600000</td>\n", "      <td>2023-08-07</td>\n", "      <td>32894</td>\n", "      <td>NaN</td>\n", "      <td>7.53</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>buy</td>\n", "      <td>000001</td>\n", "      <td>2023-08-08</td>\n", "      <td>20670</td>\n", "      <td>NaN</td>\n", "      <td>12.10</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>buy</td>\n", "      <td>600000</td>\n", "      <td>2023-08-08</td>\n", "      <td>33425</td>\n", "      <td>NaN</td>\n", "      <td>7.49</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>sell</td>\n", "      <td>000001</td>\n", "      <td>2023-08-11</td>\n", "      <td>20670</td>\n", "      <td>NaN</td>\n", "      <td>12.08</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>sell</td>\n", "      <td>600000</td>\n", "      <td>2023-08-11</td>\n", "      <td>33425</td>\n", "      <td>NaN</td>\n", "      <td>7.34</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>buy</td>\n", "      <td>000001</td>\n", "      <td>2023-08-14</td>\n", "      <td>20911</td>\n", "      <td>NaN</td>\n", "      <td>11.68</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>buy</td>\n", "      <td>600000</td>\n", "      <td>2023-08-14</td>\n", "      <td>34390</td>\n", "      <td>NaN</td>\n", "      <td>7.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>sell</td>\n", "      <td>000001</td>\n", "      <td>2023-08-17</td>\n", "      <td>20911</td>\n", "      <td>NaN</td>\n", "      <td>11.58</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>sell</td>\n", "      <td>600000</td>\n", "      <td>2023-08-17</td>\n", "      <td>34390</td>\n", "      <td>NaN</td>\n", "      <td>7.10</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>buy</td>\n", "      <td>000001</td>\n", "      <td>2023-08-18</td>\n", "      <td>21370</td>\n", "      <td>NaN</td>\n", "      <td>11.64</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>buy</td>\n", "      <td>600000</td>\n", "      <td>2023-08-18</td>\n", "      <td>34516</td>\n", "      <td>NaN</td>\n", "      <td>7.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>sell</td>\n", "      <td>000001</td>\n", "      <td>2023-08-23</td>\n", "      <td>21370</td>\n", "      <td>NaN</td>\n", "      <td>11.33</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>sell</td>\n", "      <td>600000</td>\n", "      <td>2023-08-23</td>\n", "      <td>34516</td>\n", "      <td>NaN</td>\n", "      <td>7.04</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>buy</td>\n", "      <td>000001</td>\n", "      <td>2023-08-24</td>\n", "      <td>21560</td>\n", "      <td>NaN</td>\n", "      <td>11.18</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>buy</td>\n", "      <td>600000</td>\n", "      <td>2023-08-24</td>\n", "      <td>34552</td>\n", "      <td>NaN</td>\n", "      <td>7.02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>sell</td>\n", "      <td>000001</td>\n", "      <td>2023-08-29</td>\n", "      <td>21560</td>\n", "      <td>NaN</td>\n", "      <td>11.31</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>sell</td>\n", "      <td>600000</td>\n", "      <td>2023-08-29</td>\n", "      <td>34552</td>\n", "      <td>NaN</td>\n", "      <td>7.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>buy</td>\n", "      <td>000001</td>\n", "      <td>2023-08-30</td>\n", "      <td>21723</td>\n", "      <td>NaN</td>\n", "      <td>11.20</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>buy</td>\n", "      <td>600000</td>\n", "      <td>2023-08-30</td>\n", "      <td>34751</td>\n", "      <td>NaN</td>\n", "      <td>7.06</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    type  symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                \n", "1    buy  000001 2023-08-02   20559          NaN       12.08   0.0\n", "2    buy  600000 2023-08-02   32894          NaN        7.51   0.0\n", "3   sell  000001 2023-08-07   20559          NaN       12.18   0.0\n", "4   sell  600000 2023-08-07   32894          NaN        7.53   0.0\n", "5    buy  000001 2023-08-08   20670          NaN       12.10   0.0\n", "6    buy  600000 2023-08-08   33425          NaN        7.49   0.0\n", "7   sell  000001 2023-08-11   20670          NaN       12.08   0.0\n", "8   sell  600000 2023-08-11   33425          NaN        7.34   0.0\n", "9    buy  000001 2023-08-14   20911          NaN       11.68   0.0\n", "10   buy  600000 2023-08-14   34390          NaN        7.12   0.0\n", "11  sell  000001 2023-08-17   20911          NaN       11.58   0.0\n", "12  sell  600000 2023-08-17   34390          NaN        7.10   0.0\n", "13   buy  000001 2023-08-18   21370          NaN       11.64   0.0\n", "14   buy  600000 2023-08-18   34516          NaN        7.12   0.0\n", "15  sell  000001 2023-08-23   21370          NaN       11.33   0.0\n", "16  sell  600000 2023-08-23   34516          NaN        7.04   0.0\n", "17   buy  000001 2023-08-24   21560          NaN       11.18   0.0\n", "18   buy  600000 2023-08-24   34552          NaN        7.02   0.0\n", "19  sell  000001 2023-08-29   21560          NaN       11.31   0.0\n", "20  sell  600000 2023-08-29   34552          NaN        7.12   0.0\n", "21   buy  000001 2023-08-30   21723          NaN       11.20   0.0\n", "22   buy  600000 2023-08-30   34751          NaN        7.06   0.0"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cash</th>\n", "      <th>equity</th>\n", "      <th>margin</th>\n", "      <th>market_value</th>\n", "      <th>pnl</th>\n", "      <th>unrealized_pnl</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-08-01</th>\n", "      <td>500000.00</td>\n", "      <td>500000.00</td>\n", "      <td>0.0</td>\n", "      <td>500000.00</td>\n", "      <td>0.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-02</th>\n", "      <td>4613.34</td>\n", "      <td>497985.23</td>\n", "      <td>0.0</td>\n", "      <td>497985.23</td>\n", "      <td>-2014.77</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-03</th>\n", "      <td>4613.34</td>\n", "      <td>506784.45</td>\n", "      <td>0.0</td>\n", "      <td>506784.45</td>\n", "      <td>6784.45</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-04</th>\n", "      <td>4613.34</td>\n", "      <td>506825.56</td>\n", "      <td>0.0</td>\n", "      <td>506825.56</td>\n", "      <td>6825.56</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-07</th>\n", "      <td>502713.78</td>\n", "      <td>502713.78</td>\n", "      <td>0.0</td>\n", "      <td>502713.78</td>\n", "      <td>2713.78</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-08</th>\n", "      <td>2253.53</td>\n", "      <td>503716.53</td>\n", "      <td>0.0</td>\n", "      <td>503716.53</td>\n", "      <td>3716.53</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-09</th>\n", "      <td>2253.53</td>\n", "      <td>506166.18</td>\n", "      <td>0.0</td>\n", "      <td>506166.18</td>\n", "      <td>6166.18</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-10</th>\n", "      <td>2253.53</td>\n", "      <td>501614.23</td>\n", "      <td>0.0</td>\n", "      <td>501614.23</td>\n", "      <td>1614.23</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-11</th>\n", "      <td>497286.63</td>\n", "      <td>497286.63</td>\n", "      <td>0.0</td>\n", "      <td>497286.63</td>\n", "      <td>-2713.37</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-14</th>\n", "      <td>8189.35</td>\n", "      <td>496389.72</td>\n", "      <td>0.0</td>\n", "      <td>496389.72</td>\n", "      <td>-3610.28</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-15</th>\n", "      <td>8189.35</td>\n", "      <td>500051.68</td>\n", "      <td>0.0</td>\n", "      <td>500051.68</td>\n", "      <td>51.68</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-16</th>\n", "      <td>8189.35</td>\n", "      <td>499019.98</td>\n", "      <td>0.0</td>\n", "      <td>499019.98</td>\n", "      <td>-980.02</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-17</th>\n", "      <td>494507.73</td>\n", "      <td>494507.73</td>\n", "      <td>0.0</td>\n", "      <td>494507.73</td>\n", "      <td>-5492.27</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-18</th>\n", "      <td>7.01</td>\n", "      <td>491072.33</td>\n", "      <td>0.0</td>\n", "      <td>491072.33</td>\n", "      <td>-8927.67</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-21</th>\n", "      <td>7.01</td>\n", "      <td>482146.77</td>\n", "      <td>0.0</td>\n", "      <td>482146.77</td>\n", "      <td>-17853.23</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-22</th>\n", "      <td>7.01</td>\n", "      <td>484941.07</td>\n", "      <td>0.0</td>\n", "      <td>484941.07</td>\n", "      <td>-15058.93</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-23</th>\n", "      <td>485121.75</td>\n", "      <td>485121.75</td>\n", "      <td>0.0</td>\n", "      <td>485121.75</td>\n", "      <td>-14878.25</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-24</th>\n", "      <td>1525.91</td>\n", "      <td>483007.19</td>\n", "      <td>0.0</td>\n", "      <td>483007.19</td>\n", "      <td>-16992.81</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-25</th>\n", "      <td>1525.91</td>\n", "      <td>487236.31</td>\n", "      <td>0.0</td>\n", "      <td>487236.31</td>\n", "      <td>-12763.69</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-28</th>\n", "      <td>1525.91</td>\n", "      <td>496943.91</td>\n", "      <td>0.0</td>\n", "      <td>496943.91</td>\n", "      <td>-3056.09</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-29</th>\n", "      <td>491379.75</td>\n", "      <td>491379.75</td>\n", "      <td>0.0</td>\n", "      <td>491379.75</td>\n", "      <td>-8620.25</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-08-30</th>\n", "      <td>2740.09</td>\n", "      <td>488816.61</td>\n", "      <td>0.0</td>\n", "      <td>488816.61</td>\n", "      <td>-11183.39</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 cash     equity  margin  market_value       pnl  \\\n", "date                                                               \n", "2023-08-01  500000.00  500000.00     0.0     500000.00      0.00   \n", "2023-08-02    4613.34  497985.23     0.0     497985.23  -2014.77   \n", "2023-08-03    4613.34  506784.45     0.0     506784.45   6784.45   \n", "2023-08-04    4613.34  506825.56     0.0     506825.56   6825.56   \n", "2023-08-07  502713.78  502713.78     0.0     502713.78   2713.78   \n", "2023-08-08    2253.53  503716.53     0.0     503716.53   3716.53   \n", "2023-08-09    2253.53  506166.18     0.0     506166.18   6166.18   \n", "2023-08-10    2253.53  501614.23     0.0     501614.23   1614.23   \n", "2023-08-11  497286.63  497286.63     0.0     497286.63  -2713.37   \n", "2023-08-14    8189.35  496389.72     0.0     496389.72  -3610.28   \n", "2023-08-15    8189.35  500051.68     0.0     500051.68     51.68   \n", "2023-08-16    8189.35  499019.98     0.0     499019.98   -980.02   \n", "2023-08-17  494507.73  494507.73     0.0     494507.73  -5492.27   \n", "2023-08-18       7.01  491072.33     0.0     491072.33  -8927.67   \n", "2023-08-21       7.01  482146.77     0.0     482146.77 -17853.23   \n", "2023-08-22       7.01  484941.07     0.0     484941.07 -15058.93   \n", "2023-08-23  485121.75  485121.75     0.0     485121.75 -14878.25   \n", "2023-08-24    1525.91  483007.19     0.0     483007.19 -16992.81   \n", "2023-08-25    1525.91  487236.31     0.0     487236.31 -12763.69   \n", "2023-08-28    1525.91  496943.91     0.0     496943.91  -3056.09   \n", "2023-08-29  491379.75  491379.75     0.0     491379.75  -8620.25   \n", "2023-08-30    2740.09  488816.61     0.0     488816.61 -11183.39   \n", "\n", "            unrealized_pnl  fees  \n", "date                              \n", "2023-08-01             0.0   0.0  \n", "2023-08-02             0.0   0.0  \n", "2023-08-03             0.0   0.0  \n", "2023-08-04             0.0   0.0  \n", "2023-08-07             0.0   0.0  \n", "2023-08-08             0.0   0.0  \n", "2023-08-09             0.0   0.0  \n", "2023-08-10             0.0   0.0  \n", "2023-08-11             0.0   0.0  \n", "2023-08-14             0.0   0.0  \n", "2023-08-15             0.0   0.0  \n", "2023-08-16             0.0   0.0  \n", "2023-08-17             0.0   0.0  \n", "2023-08-18             0.0   0.0  \n", "2023-08-21             0.0   0.0  \n", "2023-08-22             0.0   0.0  \n", "2023-08-23             0.0   0.0  \n", "2023-08-24             0.0   0.0  \n", "2023-08-25             0.0   0.0  \n", "2023-08-28             0.0   0.0  \n", "2023-08-29             0.0   0.0  \n", "2023-08-30             0.0   0.0  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["result.portfolio"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>long_shares</th>\n", "      <th>short_shares</th>\n", "      <th>close</th>\n", "      <th>equity</th>\n", "      <th>market_value</th>\n", "      <th>margin</th>\n", "      <th>unrealized_pnl</th>\n", "    </tr>\n", "    <tr>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-02</th>\n", "      <td>20559</td>\n", "      <td>0</td>\n", "      <td>12.03</td>\n", "      <td>247324.77</td>\n", "      <td>247324.77</td>\n", "      <td>0.0</td>\n", "      <td>-1027.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-02</th>\n", "      <td>32894</td>\n", "      <td>0</td>\n", "      <td>7.48</td>\n", "      <td>246047.12</td>\n", "      <td>246047.12</td>\n", "      <td>0.0</td>\n", "      <td>-986.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-03</th>\n", "      <td>20559</td>\n", "      <td>0</td>\n", "      <td>12.33</td>\n", "      <td>253492.47</td>\n", "      <td>253492.47</td>\n", "      <td>0.0</td>\n", "      <td>5139.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-03</th>\n", "      <td>32894</td>\n", "      <td>0</td>\n", "      <td>7.56</td>\n", "      <td>248678.64</td>\n", "      <td>248678.64</td>\n", "      <td>0.0</td>\n", "      <td>1644.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-04</th>\n", "      <td>20559</td>\n", "      <td>0</td>\n", "      <td>12.30</td>\n", "      <td>252875.70</td>\n", "      <td>252875.70</td>\n", "      <td>0.0</td>\n", "      <td>4522.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-04</th>\n", "      <td>32894</td>\n", "      <td>0</td>\n", "      <td>7.58</td>\n", "      <td>249336.52</td>\n", "      <td>249336.52</td>\n", "      <td>0.0</td>\n", "      <td>2302.58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-08</th>\n", "      <td>20670</td>\n", "      <td>0</td>\n", "      <td>12.10</td>\n", "      <td>250107.00</td>\n", "      <td>250107.00</td>\n", "      <td>0.0</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-08</th>\n", "      <td>33425</td>\n", "      <td>0</td>\n", "      <td>7.52</td>\n", "      <td>251356.00</td>\n", "      <td>251356.00</td>\n", "      <td>0.0</td>\n", "      <td>1002.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-09</th>\n", "      <td>20670</td>\n", "      <td>0</td>\n", "      <td>12.17</td>\n", "      <td>251553.90</td>\n", "      <td>251553.90</td>\n", "      <td>0.0</td>\n", "      <td>1446.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-09</th>\n", "      <td>33425</td>\n", "      <td>0</td>\n", "      <td>7.55</td>\n", "      <td>252358.75</td>\n", "      <td>252358.75</td>\n", "      <td>0.0</td>\n", "      <td>2005.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-10</th>\n", "      <td>20670</td>\n", "      <td>0</td>\n", "      <td>12.16</td>\n", "      <td>251347.20</td>\n", "      <td>251347.20</td>\n", "      <td>0.0</td>\n", "      <td>1240.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-10</th>\n", "      <td>33425</td>\n", "      <td>0</td>\n", "      <td>7.42</td>\n", "      <td>248013.50</td>\n", "      <td>248013.50</td>\n", "      <td>0.0</td>\n", "      <td>-2339.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-14</th>\n", "      <td>20911</td>\n", "      <td>0</td>\n", "      <td>11.67</td>\n", "      <td>244031.37</td>\n", "      <td>244031.37</td>\n", "      <td>0.0</td>\n", "      <td>-209.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-14</th>\n", "      <td>34390</td>\n", "      <td>0</td>\n", "      <td>7.10</td>\n", "      <td>244169.00</td>\n", "      <td>244169.00</td>\n", "      <td>0.0</td>\n", "      <td>-687.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-15</th>\n", "      <td>20911</td>\n", "      <td>0</td>\n", "      <td>11.73</td>\n", "      <td>245286.03</td>\n", "      <td>245286.03</td>\n", "      <td>0.0</td>\n", "      <td>1045.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-15</th>\n", "      <td>34390</td>\n", "      <td>0</td>\n", "      <td>7.17</td>\n", "      <td>246576.30</td>\n", "      <td>246576.30</td>\n", "      <td>0.0</td>\n", "      <td>1719.50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-16</th>\n", "      <td>20911</td>\n", "      <td>0</td>\n", "      <td>11.73</td>\n", "      <td>245286.03</td>\n", "      <td>245286.03</td>\n", "      <td>0.0</td>\n", "      <td>1045.55</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-16</th>\n", "      <td>34390</td>\n", "      <td>0</td>\n", "      <td>7.14</td>\n", "      <td>245544.60</td>\n", "      <td>245544.60</td>\n", "      <td>0.0</td>\n", "      <td>687.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-18</th>\n", "      <td>21370</td>\n", "      <td>0</td>\n", "      <td>11.56</td>\n", "      <td>247037.20</td>\n", "      <td>247037.20</td>\n", "      <td>0.0</td>\n", "      <td>-1709.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-18</th>\n", "      <td>34516</td>\n", "      <td>0</td>\n", "      <td>7.07</td>\n", "      <td>244028.12</td>\n", "      <td>244028.12</td>\n", "      <td>0.0</td>\n", "      <td>-1725.80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-21</th>\n", "      <td>21370</td>\n", "      <td>0</td>\n", "      <td>11.32</td>\n", "      <td>241908.40</td>\n", "      <td>241908.40</td>\n", "      <td>0.0</td>\n", "      <td>-6838.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-21</th>\n", "      <td>34516</td>\n", "      <td>0</td>\n", "      <td>6.96</td>\n", "      <td>240231.36</td>\n", "      <td>240231.36</td>\n", "      <td>0.0</td>\n", "      <td>-5522.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-22</th>\n", "      <td>21370</td>\n", "      <td>0</td>\n", "      <td>11.37</td>\n", "      <td>242976.90</td>\n", "      <td>242976.90</td>\n", "      <td>0.0</td>\n", "      <td>-5769.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-22</th>\n", "      <td>34516</td>\n", "      <td>0</td>\n", "      <td>7.01</td>\n", "      <td>241957.16</td>\n", "      <td>241957.16</td>\n", "      <td>0.0</td>\n", "      <td>-3796.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-24</th>\n", "      <td>21560</td>\n", "      <td>0</td>\n", "      <td>11.13</td>\n", "      <td>239962.80</td>\n", "      <td>239962.80</td>\n", "      <td>0.0</td>\n", "      <td>-1078.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-24</th>\n", "      <td>34552</td>\n", "      <td>0</td>\n", "      <td>6.99</td>\n", "      <td>241518.48</td>\n", "      <td>241518.48</td>\n", "      <td>0.0</td>\n", "      <td>-1036.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-25</th>\n", "      <td>21560</td>\n", "      <td>0</td>\n", "      <td>11.23</td>\n", "      <td>242118.80</td>\n", "      <td>242118.80</td>\n", "      <td>0.0</td>\n", "      <td>1078.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-25</th>\n", "      <td>34552</td>\n", "      <td>0</td>\n", "      <td>7.05</td>\n", "      <td>243591.60</td>\n", "      <td>243591.60</td>\n", "      <td>0.0</td>\n", "      <td>1036.56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-28</th>\n", "      <td>21560</td>\n", "      <td>0</td>\n", "      <td>11.52</td>\n", "      <td>248371.20</td>\n", "      <td>248371.20</td>\n", "      <td>0.0</td>\n", "      <td>7330.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-28</th>\n", "      <td>34552</td>\n", "      <td>0</td>\n", "      <td>7.15</td>\n", "      <td>247046.80</td>\n", "      <td>247046.80</td>\n", "      <td>0.0</td>\n", "      <td>4491.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>000001</th>\n", "      <th>2023-08-30</th>\n", "      <td>21723</td>\n", "      <td>0</td>\n", "      <td>11.13</td>\n", "      <td>241776.99</td>\n", "      <td>241776.99</td>\n", "      <td>0.0</td>\n", "      <td>-1520.61</td>\n", "    </tr>\n", "    <tr>\n", "      <th>600000</th>\n", "      <th>2023-08-30</th>\n", "      <td>34751</td>\n", "      <td>0</td>\n", "      <td>7.03</td>\n", "      <td>244299.53</td>\n", "      <td>244299.53</td>\n", "      <td>0.0</td>\n", "      <td>-1042.53</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   long_shares  short_shares  close     equity  market_value  \\\n", "symbol date                                                                    \n", "000001 2023-08-02        20559             0  12.03  247324.77     247324.77   \n", "600000 2023-08-02        32894             0   7.48  246047.12     246047.12   \n", "000001 2023-08-03        20559             0  12.33  253492.47     253492.47   \n", "600000 2023-08-03        32894             0   7.56  248678.64     248678.64   \n", "000001 2023-08-04        20559             0  12.30  252875.70     252875.70   \n", "600000 2023-08-04        32894             0   7.58  249336.52     249336.52   \n", "000001 2023-08-08        20670             0  12.10  250107.00     250107.00   \n", "600000 2023-08-08        33425             0   7.52  251356.00     251356.00   \n", "000001 2023-08-09        20670             0  12.17  251553.90     251553.90   \n", "600000 2023-08-09        33425             0   7.55  252358.75     252358.75   \n", "000001 2023-08-10        20670             0  12.16  251347.20     251347.20   \n", "600000 2023-08-10        33425             0   7.42  248013.50     248013.50   \n", "000001 2023-08-14        20911             0  11.67  244031.37     244031.37   \n", "600000 2023-08-14        34390             0   7.10  244169.00     244169.00   \n", "000001 2023-08-15        20911             0  11.73  245286.03     245286.03   \n", "600000 2023-08-15        34390             0   7.17  246576.30     246576.30   \n", "000001 2023-08-16        20911             0  11.73  245286.03     245286.03   \n", "600000 2023-08-16        34390             0   7.14  245544.60     245544.60   \n", "000001 2023-08-18        21370             0  11.56  247037.20     247037.20   \n", "600000 2023-08-18        34516             0   7.07  244028.12     244028.12   \n", "000001 2023-08-21        21370             0  11.32  241908.40     241908.40   \n", "600000 2023-08-21        34516             0   6.96  240231.36     240231.36   \n", "000001 2023-08-22        21370             0  11.37  242976.90     242976.90   \n", "600000 2023-08-22        34516             0   7.01  241957.16     241957.16   \n", "000001 2023-08-24        21560             0  11.13  239962.80     239962.80   \n", "600000 2023-08-24        34552             0   6.99  241518.48     241518.48   \n", "000001 2023-08-25        21560             0  11.23  242118.80     242118.80   \n", "600000 2023-08-25        34552             0   7.05  243591.60     243591.60   \n", "000001 2023-08-28        21560             0  11.52  248371.20     248371.20   \n", "600000 2023-08-28        34552             0   7.15  247046.80     247046.80   \n", "000001 2023-08-30        21723             0  11.13  241776.99     241776.99   \n", "600000 2023-08-30        34751             0   7.03  244299.53     244299.53   \n", "\n", "                   margin  unrealized_pnl  \n", "symbol date                                \n", "000001 2023-08-02     0.0        -1027.95  \n", "600000 2023-08-02     0.0         -986.82  \n", "000001 2023-08-03     0.0         5139.75  \n", "600000 2023-08-03     0.0         1644.70  \n", "000001 2023-08-04     0.0         4522.98  \n", "600000 2023-08-04     0.0         2302.58  \n", "000001 2023-08-08     0.0            0.00  \n", "600000 2023-08-08     0.0         1002.75  \n", "000001 2023-08-09     0.0         1446.90  \n", "600000 2023-08-09     0.0         2005.50  \n", "000001 2023-08-10     0.0         1240.20  \n", "600000 2023-08-10     0.0        -2339.75  \n", "000001 2023-08-14     0.0         -209.11  \n", "600000 2023-08-14     0.0         -687.80  \n", "000001 2023-08-15     0.0         1045.55  \n", "600000 2023-08-15     0.0         1719.50  \n", "000001 2023-08-16     0.0         1045.55  \n", "600000 2023-08-16     0.0          687.80  \n", "000001 2023-08-18     0.0        -1709.60  \n", "600000 2023-08-18     0.0        -1725.80  \n", "000001 2023-08-21     0.0        -6838.40  \n", "600000 2023-08-21     0.0        -5522.56  \n", "000001 2023-08-22     0.0        -5769.90  \n", "600000 2023-08-22     0.0        -3796.76  \n", "000001 2023-08-24     0.0        -1078.00  \n", "600000 2023-08-24     0.0        -1036.56  \n", "000001 2023-08-25     0.0         1078.00  \n", "600000 2023-08-25     0.0         1036.56  \n", "000001 2023-08-28     0.0         7330.40  \n", "600000 2023-08-28     0.0         4491.76  \n", "000001 2023-08-30     0.0        -1520.61  \n", "600000 2023-08-30     0.0        -1042.53  "]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["result.positions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>entry_date</th>\n", "      <th>exit_date</th>\n", "      <th>entry</th>\n", "      <th>exit</th>\n", "      <th>shares</th>\n", "      <th>pnl</th>\n", "      <th>return_pct</th>\n", "      <th>agg_pnl</th>\n", "      <th>bars</th>\n", "      <th>pnl_per_bar</th>\n", "      <th>stop</th>\n", "      <th>mae</th>\n", "      <th>mfe</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>long</td>\n", "      <td>000001</td>\n", "      <td>2023-08-02</td>\n", "      <td>2023-08-07</td>\n", "      <td>12.08</td>\n", "      <td>12.18</td>\n", "      <td>20559</td>\n", "      <td>2055.90</td>\n", "      <td>0.83</td>\n", "      <td>2055.90</td>\n", "      <td>3</td>\n", "      <td>685.30</td>\n", "      <td>bar</td>\n", "      <td>-0.15</td>\n", "      <td>0.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>long</td>\n", "      <td>600000</td>\n", "      <td>2023-08-02</td>\n", "      <td>2023-08-07</td>\n", "      <td>7.51</td>\n", "      <td>7.53</td>\n", "      <td>32894</td>\n", "      <td>657.88</td>\n", "      <td>0.27</td>\n", "      <td>2713.78</td>\n", "      <td>3</td>\n", "      <td>219.29</td>\n", "      <td>bar</td>\n", "      <td>-0.08</td>\n", "      <td>0.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>long</td>\n", "      <td>000001</td>\n", "      <td>2023-08-08</td>\n", "      <td>2023-08-11</td>\n", "      <td>12.10</td>\n", "      <td>12.08</td>\n", "      <td>20670</td>\n", "      <td>-413.40</td>\n", "      <td>-0.17</td>\n", "      <td>2300.38</td>\n", "      <td>3</td>\n", "      <td>-137.80</td>\n", "      <td>bar</td>\n", "      <td>-0.10</td>\n", "      <td>0.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>long</td>\n", "      <td>600000</td>\n", "      <td>2023-08-08</td>\n", "      <td>2023-08-11</td>\n", "      <td>7.49</td>\n", "      <td>7.34</td>\n", "      <td>33425</td>\n", "      <td>-5013.75</td>\n", "      <td>-2.00</td>\n", "      <td>-2713.37</td>\n", "      <td>3</td>\n", "      <td>-1671.25</td>\n", "      <td>bar</td>\n", "      <td>-0.15</td>\n", "      <td>0.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>long</td>\n", "      <td>000001</td>\n", "      <td>2023-08-14</td>\n", "      <td>2023-08-17</td>\n", "      <td>11.68</td>\n", "      <td>11.58</td>\n", "      <td>20911</td>\n", "      <td>-2091.10</td>\n", "      <td>-0.86</td>\n", "      <td>-4804.47</td>\n", "      <td>3</td>\n", "      <td>-697.03</td>\n", "      <td>bar</td>\n", "      <td>-0.10</td>\n", "      <td>0.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>long</td>\n", "      <td>600000</td>\n", "      <td>2023-08-14</td>\n", "      <td>2023-08-17</td>\n", "      <td>7.12</td>\n", "      <td>7.10</td>\n", "      <td>34390</td>\n", "      <td>-687.80</td>\n", "      <td>-0.28</td>\n", "      <td>-5492.27</td>\n", "      <td>3</td>\n", "      <td>-229.27</td>\n", "      <td>bar</td>\n", "      <td>-0.07</td>\n", "      <td>0.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>long</td>\n", "      <td>000001</td>\n", "      <td>2023-08-18</td>\n", "      <td>2023-08-23</td>\n", "      <td>11.64</td>\n", "      <td>11.33</td>\n", "      <td>21370</td>\n", "      <td>-6624.70</td>\n", "      <td>-2.66</td>\n", "      <td>-12116.97</td>\n", "      <td>3</td>\n", "      <td>-2208.23</td>\n", "      <td>bar</td>\n", "      <td>-0.33</td>\n", "      <td>0.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>long</td>\n", "      <td>600000</td>\n", "      <td>2023-08-18</td>\n", "      <td>2023-08-23</td>\n", "      <td>7.12</td>\n", "      <td>7.04</td>\n", "      <td>34516</td>\n", "      <td>-2761.28</td>\n", "      <td>-1.12</td>\n", "      <td>-14878.25</td>\n", "      <td>3</td>\n", "      <td>-920.43</td>\n", "      <td>bar</td>\n", "      <td>-0.16</td>\n", "      <td>0.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>long</td>\n", "      <td>000001</td>\n", "      <td>2023-08-24</td>\n", "      <td>2023-08-29</td>\n", "      <td>11.18</td>\n", "      <td>11.31</td>\n", "      <td>21560</td>\n", "      <td>2802.80</td>\n", "      <td>1.16</td>\n", "      <td>-12075.45</td>\n", "      <td>3</td>\n", "      <td>934.27</td>\n", "      <td>bar</td>\n", "      <td>-0.13</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>long</td>\n", "      <td>600000</td>\n", "      <td>2023-08-24</td>\n", "      <td>2023-08-29</td>\n", "      <td>7.02</td>\n", "      <td>7.12</td>\n", "      <td>34552</td>\n", "      <td>3455.20</td>\n", "      <td>1.42</td>\n", "      <td>-8620.25</td>\n", "      <td>3</td>\n", "      <td>1151.73</td>\n", "      <td>bar</td>\n", "      <td>-0.05</td>\n", "      <td>0.30</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    type  symbol entry_date  exit_date  entry   exit  shares      pnl  \\\n", "id                                                                      \n", "1   long  000001 2023-08-02 2023-08-07  12.08  12.18   20559  2055.90   \n", "2   long  600000 2023-08-02 2023-08-07   7.51   7.53   32894   657.88   \n", "3   long  000001 2023-08-08 2023-08-11  12.10  12.08   20670  -413.40   \n", "4   long  600000 2023-08-08 2023-08-11   7.49   7.34   33425 -5013.75   \n", "5   long  000001 2023-08-14 2023-08-17  11.68  11.58   20911 -2091.10   \n", "6   long  600000 2023-08-14 2023-08-17   7.12   7.10   34390  -687.80   \n", "7   long  000001 2023-08-18 2023-08-23  11.64  11.33   21370 -6624.70   \n", "8   long  600000 2023-08-18 2023-08-23   7.12   7.04   34516 -2761.28   \n", "9   long  000001 2023-08-24 2023-08-29  11.18  11.31   21560  2802.80   \n", "10  long  600000 2023-08-24 2023-08-29   7.02   7.12   34552  3455.20   \n", "\n", "    return_pct   agg_pnl  bars  pnl_per_bar stop   mae   mfe  \n", "id                                                            \n", "1         0.83   2055.90     3       685.30  bar -0.15  0.45  \n", "2         0.27   2713.78     3       219.29  bar -0.08  0.14  \n", "3        -0.17   2300.38     3      -137.80  bar -0.10  0.11  \n", "4        -2.00  -2713.37     3     -1671.25  bar -0.15  0.06  \n", "5        -0.86  -4804.47     3      -697.03  bar -0.10  0.17  \n", "6        -0.28  -5492.27     3      -229.27  bar -0.07  0.08  \n", "7        -2.66 -12116.97     3     -2208.23  bar -0.33  0.10  \n", "8        -1.12 -14878.25     3      -920.43  bar -0.16  0.05  \n", "9         1.16 -12075.45     3       934.27  bar -0.13  0.75  \n", "10        1.42  -8620.25     3      1151.73  bar -0.05  0.30  "]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["result.trades"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>500000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>488816.610000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>-8620.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>-2563.140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>total_return_pct</td>\n", "      <td>-1.724050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>total_profit</td>\n", "      <td>8971.780000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>total_loss</td>\n", "      <td>-17592.030000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>total_fees</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>max_drawdown</td>\n", "      <td>-24678.790000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>max_drawdown_pct</td>\n", "      <td>-4.869287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>win_rate</td>\n", "      <td>40.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>loss_rate</td>\n", "      <td>60.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>winning_trades</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>losing_trades</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>avg_pnl</td>\n", "      <td>-862.025000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>avg_return_pct</td>\n", "      <td>-0.341000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>avg_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>avg_profit</td>\n", "      <td>2242.945000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>avg_profit_pct</td>\n", "      <td>0.920000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>avg_winning_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>avg_loss</td>\n", "      <td>-2932.005000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>avg_loss_pct</td>\n", "      <td>-1.181667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>avg_losing_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>largest_win</td>\n", "      <td>3455.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>largest_win_pct</td>\n", "      <td>1.420000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>largest_win_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>largest_loss</td>\n", "      <td>-6624.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>largest_loss_pct</td>\n", "      <td>-2.660000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>largest_loss_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>max_wins</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>max_losses</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>sharpe</td>\n", "      <td>-0.117967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>sortino</td>\n", "      <td>-0.250087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>profit_factor</td>\n", "      <td>0.746119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>ulcer_index</td>\n", "      <td>3.617931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>upi</td>\n", "      <td>-0.028605</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>equity_r2</td>\n", "      <td>0.591262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>std_error</td>\n", "      <td>7674.257024</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      name          value\n", "0              trade_count      10.000000\n", "1     initial_market_value  500000.000000\n", "2         end_market_value  488816.610000\n", "3                total_pnl   -8620.250000\n", "4           unrealized_pnl   -2563.140000\n", "5         total_return_pct      -1.724050\n", "6             total_profit    8971.780000\n", "7               total_loss  -17592.030000\n", "8               total_fees       0.000000\n", "9             max_drawdown  -24678.790000\n", "10        max_drawdown_pct      -4.869287\n", "11                win_rate      40.000000\n", "12               loss_rate      60.000000\n", "13          winning_trades       4.000000\n", "14           losing_trades       6.000000\n", "15                 avg_pnl    -862.025000\n", "16          avg_return_pct      -0.341000\n", "17          avg_trade_bars       3.000000\n", "18              avg_profit    2242.945000\n", "19          avg_profit_pct       0.920000\n", "20  avg_winning_trade_bars       3.000000\n", "21                avg_loss   -2932.005000\n", "22            avg_loss_pct      -1.181667\n", "23   avg_losing_trade_bars       3.000000\n", "24             largest_win    3455.200000\n", "25         largest_win_pct       1.420000\n", "26        largest_win_bars       3.000000\n", "27            largest_loss   -6624.700000\n", "28        largest_loss_pct      -2.660000\n", "29       largest_loss_bars       3.000000\n", "30                max_wins       2.000000\n", "31              max_losses       6.000000\n", "32                  sharpe      -0.117967\n", "33                 sortino      -0.250087\n", "34           profit_factor       0.746119\n", "35             ulcer_index       3.617931\n", "36                     upi      -0.028605\n", "37               equity_r2       0.591262\n", "38               std_error    7674.257024"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### classwork1\n", "\n", "设定，时间2023年整年，初始金额1000000，编写如下策略：\n", "\n", "\n", "* 导入002594 比亚迪单个股票数据，一开始就购买一直持有到最后\n", "\n", "* 导入002594 比亚迪和'000001' 股票数据，一开始就购买各50%，每次持有2天 \n", "\n", "查看策略的收益曲线与各项指标\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Position对象 解读\n", "\n", "```python\n", "print(pos = ctx.long_pos())\n", "\n", "Position(symbol='000001', shares=Decimal('41118'), type='long', close=Decimal('12.33'), equity=Decimal('506984.94'), market_value=Decimal('506984.94'), margin=Decimal('0'), pnl=Decimal('10279.50'), entries=deque([Entry(id=1, date=numpy.datetime64('2023-08-02T00:00:00.000000000'), symbol='000001', shares=Decimal('41118'), price=Decimal('12.08'), type='long', bars=1, stops=[Stop(id=63, symbol='000001', stop_type=<StopType.BAR: 'bar'>, pos_type='long', percent=None, points=None, bars=3, fill_price=<PriceType.MIDDLE: 'middle'>, limit_price=None, exit_price=None)], mae=Decimal('-0.15'), mfe=Decimal('0.29'))]), bars=1) \n", "```\n", "\n", "这个对象是 `Position` 类的一个实例，`Position` 类用于表示一个未平仓的持仓信息。下面我们来详细解读这个对象的各个属性：\n", "\n", "### 整体信息\n", "这是一个关于股票代码为 `000001` 的多头持仓信息，包含了持仓的基本情况、成本、收益等信息。\n", "\n", "### 具体属性解读\n", "\n", "#### 1. 基础信息\n", "- `symbol='000001'`：表示持仓的股票代码为 `000001`。\n", "- `shares=Decimal('41118')`：表示持有的该股票的数量为 41118 股，使用 `Decimal` 类型是为了更精确地处理小数。\n", "- `type='long'`：表示持仓类型为多头，即买入并持有股票，期望股票价格上涨获利。\n", "\n", "#### 2. 价格与价值信息\n", "- `close=Decimal('12.33')`：表示该股票的最新收盘价为 12.33 元。\n", "- `equity=Decimal('506984.94')`：表示该持仓的权益价值，即当前持仓的总价值，计算方式为持仓数量乘以收盘价，$41118 \\times 12.33 = 506984.94$。\n", "- `market_value=Decimal('506984.94')`：表示该持仓的市场价值，在这个例子中与权益价值相等。\n", "- `margin=Decimal('0')`：表示该持仓使用的保证金为 0，可能是全额现金买入，没有使用杠杆。\n", "\n", "#### 3. 盈亏信息\n", "- `pnl=Decimal('10279.50')`：表示该持仓的未实现盈亏，即当前持仓相对于买入成本的盈利金额。\n", "\n", "#### 4. 持仓记录信息\n", "- `entries=deque([Entry(...)])`：这是一个双端队列，包含了该持仓的所有入场记录。在这个例子中，只有一个入场记录：\n", "  - `id=1`：入场记录的唯一标识符。\n", "  - `date=numpy.datetime64('2023-08-02T00:00:00.000000000')`：表示入场日期为 2023 年 8 月 2 日。\n", "  - `symbol='00001'`：股票代码。\n", "  - `shares=Decimal('41118')`：入场时买入的股票数量。\n", "  - `price=Decimal('12.08')`：入场时的买入价格。\n", "  - `type='long'`：入场类型为多头。\n", "  - `bars=1`：表示从入场到当前经过的周期数为 1。\n", "  - `stops=[Stop(...)]`：这是一个止损或止盈策略列表，在这个例子中只有一个止损策略：\n", "    - `id=63`：止损策略的唯一标识符。\n", "    - `symbol='000001'`：股票代码。\n", "    - `stop_type=<StopType.BAR: 'bar'>`：止损类型为按周期数止损，即经过一定周期后平仓。\n", "    - `pos_type='long'`：适用于多头持仓。\n", "    - `percent=None`：没有设置按百分比止损。\n", "    - `points=None`：没有设置按点数止损。\n", "    - `bars=3`：表示经过 3 个周期后触发止损。\n", "    - `fill_price=<PriceType.MIDDLE: 'middle'>`：止损时的成交价格类型为中间价。\n", "    - `limit_price=None`：没有设置限价。\n", "    - `exit_price=None`：目前还没有触发止损，所以没有退出价格。\n", "  - `mae=Decimal('-0.15')`：最大不利偏移，即从入场到当前，股票价格相对于入场价格的最大下跌幅度为 0.15 元。\n", "  - `mfe=Decimal('0.29')`：最大有利偏移，即从入场到当前，股票价格相对于入场价格的最大上涨幅度为 0.29 元。\n", "\n", "#### 5. 持仓周期信息\n", "- `bars=1`：表示从该持仓建立到当前经过的周期数为 1。\n", "\n", "综上所述，这个 `Position` 对象描述了一个在 2023 年 8 月 2 日以 12.08 元的价格买入 41118 股股票代码为 `000001` 的多头持仓，当前收盘价为 12.33 元，盈利 10279.50 元，设置了一个经过 3 个周期后触发的止损策略。 "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## metrics_df对象解读"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>500000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>488816.610000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>-8620.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>-2563.140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>total_return_pct</td>\n", "      <td>-1.724050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>total_profit</td>\n", "      <td>8971.780000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>total_loss</td>\n", "      <td>-17592.030000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>total_fees</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>max_drawdown</td>\n", "      <td>-24678.790000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>max_drawdown_pct</td>\n", "      <td>-4.869287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>win_rate</td>\n", "      <td>40.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>loss_rate</td>\n", "      <td>60.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>winning_trades</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>losing_trades</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>avg_pnl</td>\n", "      <td>-862.025000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>avg_return_pct</td>\n", "      <td>-0.341000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>avg_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>avg_profit</td>\n", "      <td>2242.945000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>avg_profit_pct</td>\n", "      <td>0.920000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>avg_winning_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>avg_loss</td>\n", "      <td>-2932.005000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>avg_loss_pct</td>\n", "      <td>-1.181667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>avg_losing_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>largest_win</td>\n", "      <td>3455.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>largest_win_pct</td>\n", "      <td>1.420000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>largest_win_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>largest_loss</td>\n", "      <td>-6624.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>largest_loss_pct</td>\n", "      <td>-2.660000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>largest_loss_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>max_wins</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>max_losses</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>sharpe</td>\n", "      <td>-0.117967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>sortino</td>\n", "      <td>-0.250087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>profit_factor</td>\n", "      <td>0.746119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>ulcer_index</td>\n", "      <td>3.617931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>upi</td>\n", "      <td>-0.028605</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>equity_r2</td>\n", "      <td>0.591262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>std_error</td>\n", "      <td>7674.257024</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      name          value\n", "0              trade_count      10.000000\n", "1     initial_market_value  500000.000000\n", "2         end_market_value  488816.610000\n", "3                total_pnl   -8620.250000\n", "4           unrealized_pnl   -2563.140000\n", "5         total_return_pct      -1.724050\n", "6             total_profit    8971.780000\n", "7               total_loss  -17592.030000\n", "8               total_fees       0.000000\n", "9             max_drawdown  -24678.790000\n", "10        max_drawdown_pct      -4.869287\n", "11                win_rate      40.000000\n", "12               loss_rate      60.000000\n", "13          winning_trades       4.000000\n", "14           losing_trades       6.000000\n", "15                 avg_pnl    -862.025000\n", "16          avg_return_pct      -0.341000\n", "17          avg_trade_bars       3.000000\n", "18              avg_profit    2242.945000\n", "19          avg_profit_pct       0.920000\n", "20  avg_winning_trade_bars       3.000000\n", "21                avg_loss   -2932.005000\n", "22            avg_loss_pct      -1.181667\n", "23   avg_losing_trade_bars       3.000000\n", "24             largest_win    3455.200000\n", "25         largest_win_pct       1.420000\n", "26        largest_win_bars       3.000000\n", "27            largest_loss   -6624.700000\n", "28        largest_loss_pct      -2.660000\n", "29       largest_loss_bars       3.000000\n", "30                max_wins       2.000000\n", "31              max_losses       6.000000\n", "32                  sharpe      -0.117967\n", "33                 sortino      -0.250087\n", "34           profit_factor       0.746119\n", "35             ulcer_index       3.617931\n", "36                     upi      -0.028605\n", "37               equity_r2       0.591262\n", "38               std_error    7674.257024"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "这些指标是算法交易策略回测中常用的评估指标，用于衡量策略的性能、风险和盈利能力。下面是对每个指标的详细解释：\n", "\n", "### 1. 交易数量与初始/最终市值\n", "- **`trade_count`（交易次数）**：值为 10，代表在回测期间策略执行的交易总次数，包括买入和卖出操作。它反映了策略的交易活跃度。\n", "- **`initial_market_value`（初始市值）**：初始投入资金为 500000，这是回测开始时投资组合的总价值。\n", "- **`end_market_value`（最终市值）**：回测结束时投资组合价值为 488816.61，与初始市值对比可看出策略在回测期内是盈利还是亏损。\n", "\n", "### 2. 盈亏相关指标\n", "- **`total_pnl`（总盈亏）**：总盈亏为 -8620.25，即回测期间策略整体亏损 8620.25，是最终市值与初始市值的差值。\n", "- **`unrealized_pnl`（未实现盈亏）**：未实现盈亏 -2563.14，指当前持有头寸按当前市场价格计算的盈亏，尚未通过交易实现。\n", "- **`total_return_pct`（总回报率百分比）**：总回报率 -1.724050%，反映策略在回测期内的整体收益情况，计算公式为 `(最终市值 - 初始市值) / 初始市值 * 100%`。\n", "- **`total_profit`（总盈利）**：总盈利 8971.78，是所有盈利交易的盈利总和。\n", "- **`total_loss`（总亏损）**：总亏损 -17592.03，是所有亏损交易的亏损总和。\n", "\n", "### 3. 交易成本与最大回撤\n", "- **`total_fees`（总交易费用）**：总交易费用为 0，意味着回测中未考虑交易手续费、佣金等成本。\n", "- **`max_drawdown`（最大回撤）**：最大回撤 -24678.79，指在回测期间投资组合从峰值到谷底的最大损失金额，反映策略可能面临的最大风险。\n", "- **`max_drawdown_pct`（最大回撤百分比）**：最大回撤百分比 -4.869287%，是最大回撤金额相对于峰值市值的百分比，更直观体现回撤幅度。\n", "\n", "### 4. 胜率与败率\n", "- **`win_rate`（胜率）**：胜率 40%，表示盈利交易次数占总交易次数的比例，计算公式为 `盈利交易次数 / 总交易次数 * 100%`。\n", "- **`loss_rate`（败率）**：败率 60%，即亏损交易次数占总交易次数的比例，计算公式为 `亏损交易次数 / 总交易次数 * 100%`。\n", "- **`winning_trades`（盈利交易次数）**：盈利交易次数为 4，是回测中实现盈利的交易笔数。\n", "- **`losing_trades`（亏损交易次数）**：亏损交易次数为 6，是回测中出现亏损的交易笔数。\n", "\n", "### 5. 平均盈亏与持仓时间\n", "- **`avg_pnl`（平均盈亏）**：平均盈亏 -862.025，是每次交易的平均盈利或亏损金额，计算公式为 `总盈亏 / 总交易次数`。\n", "- **`avg_return_pct`（平均回报率百分比）**：平均回报率 -0.341%，是每次交易的平均回报率。\n", "- **`avg_trade_bars`（平均交易周期）**：平均交易周期为 3，指每次交易平均持有的周期数（如天数、小时数等，取决于回测的时间粒度）。\n", "- **`avg_profit`（平均盈利）**：平均盈利 2242.945，是每次盈利交易的平均盈利金额，计算公式为 `总盈利 / 盈利交易次数`。\n", "- **`avg_profit_pct`（平均盈利百分比）**：平均盈利百分比 0.92%，是每次盈利交易的平均回报率。\n", "- **`avg_winning_trade_bars`（平均盈利交易周期）**：平均盈利交易周期为 3，指每次盈利交易平均持有的周期数。\n", "- **`avg_loss`（平均亏损）**：平均亏损 -2932.005，是每次亏损交易的平均亏损金额，计算公式为 `总亏损 / 亏损交易次数`。\n", "- **`avg_loss_pct`（平均亏损百分比）**：平均亏损百分比 -1.181667%，是每次亏损交易的平均回报率。\n", "- **`avg_losing_trade_bars`（平均亏损交易周期）**：平均亏损交易周期为 3，指每次亏损交易平均持有的周期数。\n", "\n", "### 6. 最大盈利与亏损\n", "- **`largest_win`（最大盈利）**：最大盈利 3455.2，是回测期间单笔交易的最大盈利金额。\n", "- **`largest_win_pct`（最大盈利百分比）**：最大盈利百分比 1.42%，是单笔交易的最大回报率。\n", "- **`largest_win_bars`（最大盈利交易周期）**：最大盈利交易周期为 3，指最大盈利交易持有的周期数。\n", "- **`largest_loss`（最大亏损）**：最大亏损 -6624.7，是回测期间单笔交易的最大亏损金额。\n", "- **`largest_loss_pct`（最大亏损百分比）**：最大亏损百分比 -2.66%，是单笔交易的最大亏损率。\n", "- **`largest_loss_bars`（最大亏损交易周期）**：最大亏损交易周期为 3，指最大亏损交易持有的周期数。\n", "\n", "### 7. 连续盈利与亏损\n", "- **`max_wins`（最大连续盈利次数）**：最大连续盈利次数为 2，即回测期间连续盈利的最大交易次数。\n", "- **`max_losses`（最大连续亏损次数）**：最大连续亏损次数为 6，即回测期间连续亏损的最大交易次数。\n", "\n", "### 8. 风险调整指标\n", "- **`sharpe`（夏普比率）**：夏普比率 -0.117967，衡量策略在承担单位风险时所能获得的超过无风险收益的额外收益，值越高越好，负值表示策略表现不如无风险投资。\n", "- **`sortino`（索提诺比率）**：索提诺比率 -0.250087，与夏普比率类似，但只考虑下行风险，负值同样表示策略表现不佳。\n", "- **`profit_factor`（盈利因子）**：盈利因子 0.746119，是总盈利与总亏损的绝对值之比，大于 1 表示策略总体盈利，小于 1 表示总体亏损。\n", "- **`ulcer_index`（溃疡指数）**：溃疡指数 3.617931，衡量投资组合的回撤深度和持续时间，值越低表示策略的回撤控制越好。\n", "- **`upi`（溃疡绩效指数）**：溃疡绩效指数 -0.028605，是总回报率与溃疡指数的比值，反映策略在控制回撤情况下的盈利能力，正值表示较好。\n", "\n", "### 9. 拟合优度与标准误差\n", "- **`equity_r2`（权益拟合优度）**：权益拟合优度 0.591262，反映策略的权益曲线与市场基准之间的拟合程度，值越接近 1 表示拟合度越高。\n", "- **`std_error`（标准误差）**：标准误差 7674.257024，衡量策略实际表现与预期表现之间的偏离程度，值越小表示策略表现越稳定。\n", "\n", "综合来看，这个策略在回测期间整体表现不佳，处于亏损状态，胜率较低，最大回撤和连续亏损情况也不太理想，风险调整后的收益指标为负，说明策略在控制风险和获取收益方面还有待改进。 "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading bar data...\n"]}, {"ename": "KeyError", "evalue": "'000001'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[12], line 6\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m# 实例化 AKShare 数据类并获取股票数据\u001b[39;00m\n\u001b[0;32m      5\u001b[0m akshare \u001b[38;5;241m=\u001b[39m AKShare()\n\u001b[1;32m----> 6\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[43makshare\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mquery\u001b[49m\u001b[43m(\u001b[49m\u001b[43msymbols\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m000001\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstart_date\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m20200101\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mend_date\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m20230830\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43madjust\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      8\u001b[0m \u001b[38;5;66;03m# 打印获取的股票数据的前5行\u001b[39;00m\n\u001b[0;32m      9\u001b[0m df\u001b[38;5;241m.\u001b[39mhead()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\quant\\Lib\\site-packages\\pybroker\\data.py:231\u001b[0m, in \u001b[0;36mDataSource.query\u001b[1;34m(self, symbols, start_date, end_date, timeframe, adjust)\u001b[0m\n\u001b[0;32m    224\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_logger\u001b[38;5;241m.\u001b[39mdownload_bar_data_start()\n\u001b[0;32m    225\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_logger\u001b[38;5;241m.\u001b[39minfo_download_bar_data_start(\n\u001b[0;32m    226\u001b[0m     symbols\u001b[38;5;241m=\u001b[39muncached_syms,\n\u001b[0;32m    227\u001b[0m     timeframe\u001b[38;5;241m=\u001b[39mtimeframe,\n\u001b[0;32m    228\u001b[0m     start_date\u001b[38;5;241m=\u001b[39mstart_date,\n\u001b[0;32m    229\u001b[0m     end_date\u001b[38;5;241m=\u001b[39mend_date,\n\u001b[0;32m    230\u001b[0m )\n\u001b[1;32m--> 231\u001b[0m df \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_fetch_data\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    232\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mfrozenset\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43muncached_syms\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstart_date\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mend_date\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeframe\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43madjust\u001b[49m\n\u001b[0;32m    233\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    234\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[0;32m    235\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_scope\u001b[38;5;241m.\u001b[39mdata_source_cache \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    236\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m cached_df\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mempty\n\u001b[0;32m    237\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mset\u001b[39m(cached_df\u001b[38;5;241m.\u001b[39mcolumns) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mset\u001b[39m(df\u001b[38;5;241m.\u001b[39mcolumns)\n\u001b[0;32m    238\u001b[0m ):\n\u001b[0;32m    239\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_logger\u001b[38;5;241m.\u001b[39minfo_invalidate_data_source_cache()\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\quant\\Lib\\site-packages\\pybroker\\ext\\data.py:47\u001b[0m, in \u001b[0;36mAKShare._fetch_data\u001b[1;34m(self, symbols, start_date, end_date, timeframe, adjust)\u001b[0m\n\u001b[0;32m     45\u001b[0m period \u001b[38;5;241m=\u001b[39m AKShare\u001b[38;5;241m.\u001b[39m_tf_to_period[formatted_tf]\n\u001b[0;32m     46\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mlen\u001b[39m(symbols_list)):\n\u001b[1;32m---> 47\u001b[0m     temp_df \u001b[38;5;241m=\u001b[39m \u001b[43makshare\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mstock_zh_a_hist\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m     48\u001b[0m \u001b[43m        \u001b[49m\u001b[43msymbol\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msymbols_simple\u001b[49m\u001b[43m[\u001b[49m\u001b[43mi\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     49\u001b[0m \u001b[43m        \u001b[49m\u001b[43mstart_date\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstart_date_str\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     50\u001b[0m \u001b[43m        \u001b[49m\u001b[43mend_date\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mend_date_str\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     51\u001b[0m \u001b[43m        \u001b[49m\u001b[43mperiod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mperiod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m     52\u001b[0m \u001b[43m        \u001b[49m\u001b[43madjust\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43madjust\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43madjust\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mis\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m     53\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     54\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m temp_df\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mempty:\n\u001b[0;32m     55\u001b[0m         temp_df[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msymbol\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m symbols_list[i]\n", "File \u001b[1;32mc:\\Users\\<USER>\\anaconda3\\envs\\quant\\Lib\\site-packages\\akshare\\stock_feature\\stock_hist_em.py:1098\u001b[0m, in \u001b[0;36mstock_zh_a_hist\u001b[1;34m(symbol, period, start_date, end_date, adjust, timeout)\u001b[0m\n\u001b[0;32m   1090\u001b[0m period_dict \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdaily\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m101\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mweekly\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m102\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmonthly\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m103\u001b[39m\u001b[38;5;124m\"\u001b[39m}\n\u001b[0;32m   1091\u001b[0m url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttps://push2his.eastmoney.com/api/qt/stock/kline/get\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1092\u001b[0m params \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m   1093\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfields1\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mf1,f2,f3,f4,f5,f6\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   1094\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfields2\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mf51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f116\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   1095\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mut\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m7eea3edcaed734bea9cbfc24409ed989\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   1096\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mklt\u001b[39m\u001b[38;5;124m\"\u001b[39m: period_dict[period],\n\u001b[0;32m   1097\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfqt\u001b[39m\u001b[38;5;124m\"\u001b[39m: adjust_dict[adjust],\n\u001b[1;32m-> 1098\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msecid\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mcode_id_dict\u001b[49m\u001b[43m[\u001b[49m\u001b[43msymbol\u001b[49m\u001b[43m]\u001b[49m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msymbol\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   1099\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbeg\u001b[39m\u001b[38;5;124m\"\u001b[39m: start_date,\n\u001b[0;32m   1100\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mend\u001b[39m\u001b[38;5;124m\"\u001b[39m: end_date,\n\u001b[0;32m   1101\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m1623766962675\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   1102\u001b[0m }\n\u001b[0;32m   1103\u001b[0m r \u001b[38;5;241m=\u001b[39m requests\u001b[38;5;241m.\u001b[39mget(url, params\u001b[38;5;241m=\u001b[39mparams, timeout\u001b[38;5;241m=\u001b[39mtimeout)\n\u001b[0;32m   1104\u001b[0m data_json \u001b[38;5;241m=\u001b[39m r\u001b[38;5;241m.\u001b[39mjson()\n", "\u001b[1;31m<PERSON><PERSON>Error\u001b[0m: '000001'"]}], "source": ["\n", "\n", "# 定义交易策略函数\n", "def buy_func(ctx: ExecContext) -> None:\n", "    pos = ctx.long_pos()  # 获取当前的长期持有的股票\n", "    if pos:  # 如果当前持有股票\n", "        ctx.sell_shares = pos.shares  # 卖出所有的股票\n", "    else:  # 如果当前没有持有股票\n", "        ctx.buy_shares = ctx.calc_target_shares(1)  # 买入全部可购买的股票\n", "        ctx.hold_bars = 3  # 设置持有的交易日为3天\n", "\n", "\n", "# 创建策略配置对象，设置初始现金为 500,000 元\n", "my_config = StrategyConfig(initial_cash=500000)\n", "\n", "# 创建策略对象，设置数据源为 AKShare，开始日期为 '20230801'，结束日期为 '20230830'，策略配置为 my_config\n", "strategy = Strategy(data_source=AKShare(), start_date='20230801', end_date='20230830', config=my_config)\n", "\n", "# 将定义的交易策略函数添加到策略对象中，应用于股票 '000001'\n", "strategy.add_execution(fn=buy_func, symbols=['000001'])\n", "\n", "# 执行回测\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["TestResult(start_date=datetime.datetime(2023, 8, 1, 0, 0), end_date=datetime.datetime(2023, 8, 30, 0, 0), portfolio=                 cash     equity  margin  market_value       pnl  \\\n", "date                                                               \n", "2023-08-01  500000.00  500000.00     0.0     500000.00      0.00   \n", "2023-08-02    3294.56  497944.10     0.0     497944.10  -2055.90   \n", "2023-08-03  503289.44  503289.44     0.0     503289.44   3289.44   \n", "2023-08-04       3.62  500441.42     0.0     500441.42    441.42   \n", "2023-08-07  495559.10  495559.10     0.0     495559.10  -4440.90   \n", "2023-08-08    2447.80  495559.10     0.0     495559.10  -4440.90   \n", "2023-08-09  496374.16  496374.16     0.0     496374.16  -3625.84   \n", "2023-08-10    1232.12  497189.88     0.0     497189.88  -2810.12   \n", "2023-08-11  493927.00  493927.00     0.0     493927.00  -6073.00   \n", "2023-08-14    8728.12  493511.59     0.0     493511.59  -6488.41   \n", "2023-08-15  494757.82  494757.82     0.0     494757.82  -5242.18   \n", "2023-08-16       9.88  494757.82     0.0     494757.82  -5242.18   \n", "2023-08-17  488431.12  488431.12     0.0     488431.12 -11568.88   \n", "2023-08-18       5.08  485074.24     0.0     485074.24 -14925.76   \n", "2023-08-21  480458.53  480458.53     0.0     480458.53 -19541.47   \n", "2023-08-22       6.31  480036.34     0.0     480036.34 -19963.66   \n", "2023-08-23  478347.58  478347.58     0.0     478347.58 -21652.42   \n", "2023-08-24    2985.16  476221.63     0.0     476221.63 -23778.37   \n", "2023-08-25  479197.96  479197.96     0.0     479197.96 -20802.04   \n", "2023-08-28       1.34  471422.78     0.0     471422.78 -28577.22   \n", "2023-08-29  462829.16  462829.16     0.0     462829.16 -37170.84   \n", "2023-08-30    4502.76  459964.62     0.0     459964.62 -40035.38   \n", "\n", "            unrealized_pnl  fees  \n", "date                              \n", "2023-08-01             0.0   0.0  \n", "2023-08-02             0.0   0.0  \n", "2023-08-03             0.0   0.0  \n", "2023-08-04             0.0   0.0  \n", "2023-08-07             0.0   0.0  \n", "2023-08-08             0.0   0.0  \n", "2023-08-09             0.0   0.0  \n", "2023-08-10             0.0   0.0  \n", "2023-08-11             0.0   0.0  \n", "2023-08-14             0.0   0.0  \n", "2023-08-15             0.0   0.0  \n", "2023-08-16             0.0   0.0  \n", "2023-08-17             0.0   0.0  \n", "2023-08-18             0.0   0.0  \n", "2023-08-21             0.0   0.0  \n", "2023-08-22             0.0   0.0  \n", "2023-08-23             0.0   0.0  \n", "2023-08-24             0.0   0.0  \n", "2023-08-25             0.0   0.0  \n", "2023-08-28             0.0   0.0  \n", "2023-08-29             0.0   0.0  \n", "2023-08-30             0.0   0.0  , positions=                   long_shares  short_shares  close     equity  market_value  \\\n", "symbol date                                                                    \n", "000001 2023-08-02        41118             0  12.03  494649.54     494649.54   \n", "       2023-08-04        40686             0  12.30  500437.80     500437.80   \n", "       2023-08-08        40753             0  12.10  493111.30     493111.30   \n", "       2023-08-10        40786             0  12.16  495957.76     495957.76   \n", "       2023-08-14        41541             0  11.67  484783.47     484783.47   \n", "       2023-08-16        42178             0  11.73  494747.94     494747.94   \n", "       2023-08-18        41961             0  11.56  485069.16     485069.16   \n", "       2023-08-22        42219             0  11.37  480030.03     480030.03   \n", "       2023-08-24        42519             0  11.13  473236.47     473236.47   \n", "       2023-08-28        40922             0  11.52  471421.44     471421.44   \n", "       2023-08-30        40922             0  11.13  455461.86     455461.86   \n", "\n", "                   margin  unrealized_pnl  \n", "symbol date                                \n", "000001 2023-08-02     0.0        -2055.90  \n", "       2023-08-04     0.0        -2848.02  \n", "       2023-08-08     0.0            0.00  \n", "       2023-08-10     0.0          815.72  \n", "       2023-08-14     0.0         -415.41  \n", "       2023-08-16     0.0            0.00  \n", "       2023-08-18     0.0        -3356.88  \n", "       2023-08-22     0.0         -422.19  \n", "       2023-08-24     0.0        -2125.95  \n", "       2023-08-28     0.0        -7775.18  \n", "       2023-08-30     0.0        -2864.54  , orders=    type  symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                \n", "1    buy  000001 2023-08-02   41118          NaN       12.08   0.0\n", "2   sell  000001 2023-08-03   41118          NaN       12.16   0.0\n", "3    buy  000001 2023-08-04   40686          NaN       12.37   0.0\n", "4   sell  000001 2023-08-07   40686          NaN       12.18   0.0\n", "5    buy  000001 2023-08-08   40753          NaN       12.10   0.0\n", "6   sell  000001 2023-08-09   40753          NaN       12.12   0.0\n", "7    buy  000001 2023-08-10   40786          NaN       12.14   0.0\n", "8   sell  000001 2023-08-11   40786          NaN       12.08   0.0\n", "9    buy  000001 2023-08-14   41541          NaN       11.68   0.0\n", "10  sell  000001 2023-08-15   41541          NaN       11.70   0.0\n", "11   buy  000001 2023-08-16   42178          NaN       11.73   0.0\n", "12  sell  000001 2023-08-17   42178          NaN       11.58   0.0\n", "13   buy  000001 2023-08-18   41961          NaN       11.64   0.0\n", "14  sell  000001 2023-08-21   41961          NaN       11.45   0.0\n", "15   buy  000001 2023-08-22   42219          NaN       11.38   0.0\n", "16  sell  000001 2023-08-23   42219          NaN       11.33   0.0\n", "17   buy  000001 2023-08-24   42519          NaN       11.18   0.0\n", "18  sell  000001 2023-08-25   42519          NaN       11.20   0.0\n", "19   buy  000001 2023-08-28   40922          NaN       11.71   0.0\n", "20  sell  000001 2023-08-29   40922          NaN       11.31   0.0\n", "21   buy  000001 2023-08-30   40922          NaN       11.20   0.0, trades=    type  symbol entry_date  exit_date  entry   exit  shares       pnl  \\\n", "id                                                                       \n", "1   long  000001 2023-08-02 2023-08-03  12.08  12.16   41118   3289.44   \n", "2   long  000001 2023-08-04 2023-08-07  12.37  12.18   40686  -7730.34   \n", "3   long  000001 2023-08-08 2023-08-09  12.10  12.12   40753    815.06   \n", "4   long  000001 2023-08-10 2023-08-11  12.14  12.08   40786  -2447.16   \n", "5   long  000001 2023-08-14 2023-08-15  11.68  11.70   41541    830.82   \n", "6   long  000001 2023-08-16 2023-08-17  11.73  11.58   42178  -6326.70   \n", "7   long  000001 2023-08-18 2023-08-21  11.64  11.45   41961  -7972.59   \n", "8   long  000001 2023-08-22 2023-08-23  11.38  11.33   42219  -2110.95   \n", "9   long  000001 2023-08-24 2023-08-25  11.18  11.20   42519    850.38   \n", "10  long  000001 2023-08-28 2023-08-29  11.71  11.31   40922 -16368.80   \n", "\n", "    return_pct   agg_pnl  bars  pnl_per_bar  stop   mae   mfe  \n", "id                                                             \n", "1         0.66   3289.44     1      3289.44  None -0.15  0.16  \n", "2        -1.54  -4440.90     1     -7730.34  None -0.19  0.16  \n", "3         0.17  -3625.84     1       815.06  None -0.10  0.11  \n", "4        -0.49  -6073.00     1     -2447.16  None -0.08  0.07  \n", "5         0.17  -5242.18     1       830.82  None -0.09  0.10  \n", "6        -1.28 -11568.88     1     -6326.70  None -0.15  0.12  \n", "7        -1.63 -19541.47     1     -7972.59  None -0.19  0.10  \n", "8        -0.44 -21652.42     1     -2110.95  None -0.07  0.07  \n", "9         0.18 -20802.04     1       850.38  None -0.13  0.14  \n", "10       -3.42 -37170.84     1    -16368.80  None -0.40  0.22  , metrics=EvalMetrics(trade_count=10, initial_market_value=500000.0, end_market_value=459964.62, total_pnl=-37170.84, unrealized_pnl=-2864.540000000008, total_return_pct=-7.434167999999996, annual_return_pct=None, total_profit=5785.7, total_loss=-42956.54, total_fees=0.0, max_drawdown=-43324.82000000001, max_drawdown_pct=-8.608330824505305, win_rate=40.0, loss_rate=60.0, winning_trades=4, losing_trades=6, avg_pnl=-3717.084, avg_return_pct=-0.762, avg_trade_bars=1.0, avg_profit=1446.425, avg_profit_pct=0.295, avg_winning_trade_bars=1.0, avg_loss=-7159.423333333333, avg_loss_pct=-1.4666666666666668, avg_losing_trade_bars=1.0, largest_win=3289.44, largest_win_pct=0.66, largest_win_bars=1, largest_loss=-16368.8, largest_loss_pct=-3.42, largest_loss_bars=1, max_wins=1, max_losses=3, sharpe=-0.5715556797902418, sortino=-0.7869766853183464, calmar=None, profit_factor=0.21857881261020728, ulcer_index=5.0494299817594195, upi=-0.07807840435538736, equity_r2=0.8699950729154048, std_error=12031.603751648245, annual_std_error=None, annual_volatility_pct=None), metrics_df=                      name          value\n", "0              trade_count      10.000000\n", "1     initial_market_value  500000.000000\n", "2         end_market_value  459964.620000\n", "3                total_pnl  -37170.840000\n", "4           unrealized_pnl   -2864.540000\n", "5         total_return_pct      -7.434168\n", "6             total_profit    5785.700000\n", "7               total_loss  -42956.540000\n", "8               total_fees       0.000000\n", "9             max_drawdown  -43324.820000\n", "10        max_drawdown_pct      -8.608331\n", "11                win_rate      40.000000\n", "12               loss_rate      60.000000\n", "13          winning_trades       4.000000\n", "14           losing_trades       6.000000\n", "15                 avg_pnl   -3717.084000\n", "16          avg_return_pct      -0.762000\n", "17          avg_trade_bars       1.000000\n", "18              avg_profit    1446.425000\n", "19          avg_profit_pct       0.295000\n", "20  avg_winning_trade_bars       1.000000\n", "21                avg_loss   -7159.423333\n", "22            avg_loss_pct      -1.466667\n", "23   avg_losing_trade_bars       1.000000\n", "24             largest_win    3289.440000\n", "25         largest_win_pct       0.660000\n", "26        largest_win_bars       1.000000\n", "27            largest_loss  -16368.800000\n", "28        largest_loss_pct      -3.420000\n", "29       largest_loss_bars       1.000000\n", "30                max_wins       1.000000\n", "31              max_losses       3.000000\n", "32                  sharpe      -0.571556\n", "33                 sortino      -0.786977\n", "34           profit_factor       0.218579\n", "35             ulcer_index       5.049430\n", "36                     upi      -0.078078\n", "37               equity_r2       0.869995\n", "38               std_error   12031.603752, bootstrap=None, signals=None, stops=None)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2023-01-01 00:00:00 to 2023-07-01 00:00:00\n", "\n", "Loading bar data...\n", "Loaded bar data: 0:00:01 \n", "\n", "Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 2) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 50% (1 of 2) |#############             | Elapsed Time: 0:00:05 ETA:   0:00:05\n", "100% (2 of 2) |##########################| Elapsed Time: 0:00:05 Time:  0:00:05\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Test split: 2023-01-03 00:00:00 to 2023-06-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 118) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "100% (118 of 118) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:06\n", "TestResult(start_date=datetime.datetime(2023, 1, 1, 0, 0), end_date=datetime.datetime(2023, 7, 1, 0, 0), portfolio=                cash    equity  margin  market_value    pnl  unrealized_pnl  \\\n", "date                                                                          \n", "2023-01-03  100000.0  100000.0     0.0      100000.0    0.0             0.0   \n", "2023-01-04  100000.0  100000.0     0.0      100000.0    0.0             0.0   \n", "2023-01-05  100000.0  100000.0     0.0      100000.0    0.0             0.0   \n", "2023-01-06  100000.0  100000.0     0.0      100000.0    0.0             0.0   \n", "2023-01-09  100000.0  100000.0     0.0      100000.0    0.0             0.0   \n", "...              ...       ...     ...           ...    ...             ...   \n", "2023-06-26   99873.7   99873.7     0.0       99873.7 -126.3             0.0   \n", "2023-06-27   99873.7   99873.7     0.0       99873.7 -126.3             0.0   \n", "2023-06-28   99873.7   99873.7     0.0       99873.7 -126.3             0.0   \n", "2023-06-29   99873.7   99873.7     0.0       99873.7 -126.3             0.0   \n", "2023-06-30   99873.7   99873.7     0.0       99873.7 -126.3             0.0   \n", "\n", "            fees  \n", "date              \n", "2023-01-03   0.0  \n", "2023-01-04   0.0  \n", "2023-01-05   0.0  \n", "2023-01-06   0.0  \n", "2023-01-09   0.0  \n", "...          ...  \n", "2023-06-26   0.0  \n", "2023-06-27   0.0  \n", "2023-06-28   0.0  \n", "2023-06-29   0.0  \n", "2023-06-30   0.0  \n", "\n", "[118 rows x 7 columns], positions=                      long_shares  short_shares  close  equity  market_value  \\\n", "symbol    date                                                                 \n", "600000.SH 2023-02-22          100             0   7.24   724.0         724.0   \n", "          2023-02-23          100             0   7.23   723.0         723.0   \n", "          2023-02-24          100             0   7.18   718.0         718.0   \n", "          2023-02-27          100             0   7.16   716.0         716.0   \n", "          2023-02-28          100             0   7.18   718.0         718.0   \n", "000001.SZ 2023-03-02          100             0  14.24  1424.0        1424.0   \n", "          2023-03-03          100             0  14.29  1429.0        1429.0   \n", "600000.SH 2023-03-06          100             0   7.33   733.0         733.0   \n", "          2023-03-07          100             0   7.31   731.0         731.0   \n", "          2023-03-08          100             0   7.31   731.0         731.0   \n", "          2023-03-09          100             0   7.27   727.0         727.0   \n", "          2023-04-10          100             0   7.21   721.0         721.0   \n", "          2023-04-11          100             0   7.18   718.0         718.0   \n", "          2023-04-12          100             0   7.21   721.0         721.0   \n", "          2023-04-13          100             0   7.25   725.0         725.0   \n", "          2023-04-14          100             0   7.27   727.0         727.0   \n", "000001.SZ 2023-04-17          100             0  12.93  1293.0        1293.0   \n", "          2023-04-18          100             0  13.00  1300.0        1300.0   \n", "600000.SH 2023-04-18          100             0   7.54   754.0         754.0   \n", "000001.SZ 2023-04-19          100             0  12.85  1285.0        1285.0   \n", "600000.SH 2023-04-19          100             0   7.53   753.0         753.0   \n", "000001.SZ 2023-04-20          100             0  12.75  1275.0        1275.0   \n", "600000.SH 2023-04-20          100             0   7.68   768.0         768.0   \n", "          2023-04-21          100             0   7.59   759.0         759.0   \n", "          2023-04-24          100             0   7.49   749.0         749.0   \n", "          2023-05-08          100             0   8.07   807.0         807.0   \n", "000001.SZ 2023-05-09          100             0  13.16  1316.0        1316.0   \n", "600000.SH 2023-05-09          100             0   7.96   796.0         796.0   \n", "          2023-06-08          100             0   7.57   757.0         757.0   \n", "000001.SZ 2023-06-09          100             0  11.88  1188.0        1188.0   \n", "600000.SH 2023-06-09          100             0   7.56   756.0         756.0   \n", "000001.SZ 2023-06-12          100             0  11.79  1179.0        1179.0   \n", "600000.SH 2023-06-12          100             0   7.43   743.0         743.0   \n", "          2023-06-13          100             0   7.46   746.0         746.0   \n", "          2023-06-14          100             0   7.40   740.0         740.0   \n", "\n", "                      margin  unrealized_pnl  \n", "symbol    date                                \n", "600000.SH 2023-02-22     0.0            -2.0  \n", "          2023-02-23     0.0            -3.0  \n", "          2023-02-24     0.0            -8.0  \n", "          2023-02-27     0.0           -10.0  \n", "          2023-02-28     0.0            -8.0  \n", "000001.SZ 2023-03-02     0.0            -1.0  \n", "          2023-03-03     0.0             4.0  \n", "600000.SH 2023-03-06     0.0            -1.0  \n", "          2023-03-07     0.0            -3.0  \n", "          2023-03-08     0.0            -3.0  \n", "          2023-03-09     0.0            -7.0  \n", "          2023-04-10     0.0            -1.0  \n", "          2023-04-11     0.0            -4.0  \n", "          2023-04-12     0.0            -1.0  \n", "          2023-04-13     0.0             3.0  \n", "          2023-04-14     0.0             5.0  \n", "000001.SZ 2023-04-17     0.0            16.0  \n", "          2023-04-18     0.0            23.0  \n", "600000.SH 2023-04-18     0.0             6.0  \n", "000001.SZ 2023-04-19     0.0             8.0  \n", "600000.SH 2023-04-19     0.0             5.0  \n", "000001.SZ 2023-04-20     0.0            -2.0  \n", "600000.SH 2023-04-20     0.0            20.0  \n", "          2023-04-21     0.0            11.0  \n", "          2023-04-24     0.0             1.0  \n", "          2023-05-08     0.0            11.0  \n", "000001.SZ 2023-05-09     0.0           -20.0  \n", "600000.SH 2023-05-09     0.0             0.0  \n", "          2023-06-08     0.0             6.0  \n", "000001.SZ 2023-06-09     0.0            -9.0  \n", "600000.SH 2023-06-09     0.0             5.0  \n", "000001.SZ 2023-06-12     0.0           -18.0  \n", "600000.SH 2023-06-12     0.0            -8.0  \n", "          2023-06-13     0.0            -5.0  \n", "          2023-06-14     0.0           -11.0  , orders=    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  600000.SH 2023-02-22     100          NaN        7.26   0.0\n", "2   sell  600000.SH 2023-03-01     100          NaN        7.22   0.0\n", "3    buy  000001.SZ 2023-03-02     100          NaN       14.25   0.0\n", "4   sell  000001.SZ 2023-03-06     100          NaN       13.97   0.0\n", "5    buy  600000.SH 2023-03-06     100          NaN        7.34   0.0\n", "6   sell  600000.SH 2023-03-10     100          NaN        7.19   0.0\n", "7    buy  600000.SH 2023-04-10     100          NaN        7.22   0.0\n", "8   sell  600000.SH 2023-04-17     100          NaN        7.33   0.0\n", "9    buy  000001.SZ 2023-04-17     100          NaN       12.77   0.0\n", "10   buy  600000.SH 2023-04-18     100          NaN        7.48   0.0\n", "11  sell  000001.SZ 2023-04-21     100          NaN       12.51   0.0\n", "12  sell  600000.SH 2023-04-25     100          NaN        7.57   0.0\n", "13   buy  600000.SH 2023-05-08     100          NaN        7.96   0.0\n", "14   buy  000001.SZ 2023-05-09     100          NaN       13.36   0.0\n", "15  sell  600000.SH 2023-05-10     100          NaN        7.80   0.0\n", "16  sell  000001.SZ 2023-05-10     100          NaN       13.09   0.0\n", "17   buy  600000.SH 2023-06-08     100          NaN        7.51   0.0\n", "18   buy  000001.SZ 2023-06-09     100          NaN       11.97   0.0\n", "19  sell  000001.SZ 2023-06-13     100          NaN       11.73   0.0\n", "20  sell  600000.SH 2023-06-15     100          NaN        7.44   0.0, trades=    type     symbol entry_date  exit_date  entry   exit  shares    pnl  \\\n", "id                                                                       \n", "1   long  600000.SH 2023-02-22 2023-03-01   7.26   7.22     100  -4.00   \n", "2   long  000001.SZ 2023-03-02 2023-03-06  14.25  13.97     100 -28.50   \n", "3   long  600000.SH 2023-03-06 2023-03-10   7.34   7.19     100 -14.68   \n", "4   long  600000.SH 2023-04-10 2023-04-17   7.22   7.33     100  11.00   \n", "5   long  000001.SZ 2023-04-17 2023-04-21  12.77  12.51     100 -25.54   \n", "6   long  600000.SH 2023-04-18 2023-04-25   7.48   7.57     100   9.00   \n", "7   long  600000.SH 2023-05-08 2023-05-10   7.96   7.80     100 -15.92   \n", "8   long  000001.SZ 2023-05-09 2023-05-10  13.36  13.09     100 -26.72   \n", "9   long  000001.SZ 2023-06-09 2023-06-13  11.97  11.73     100 -23.94   \n", "10  long  600000.SH 2023-06-08 2023-06-15   7.51   7.44     100  -7.00   \n", "\n", "    return_pct  agg_pnl  bars  pnl_per_bar  stop   mae   mfe  \n", "id                                                            \n", "1        -0.55    -4.00     5        -0.80   bar -0.12  0.03  \n", "2        -2.00   -32.50     2       -14.25  loss -0.29  0.19  \n", "3        -2.00   -47.18     4        -3.67  loss -0.15  0.12  \n", "4         1.52   -36.18     5         2.20   bar -0.05  0.11  \n", "5        -2.00   -61.72     4        -6.39  loss -0.26  0.43  \n", "6         1.20   -52.72     5         1.80   bar -0.11  0.25  \n", "7        -2.00   -68.64     2        -7.96  loss -0.20  0.26  \n", "8        -2.00   -95.36     1       -26.72  loss -0.27  0.24  \n", "9        -2.00  -119.30     2       -11.97  loss -0.24  0.12  \n", "10       -0.93  -126.30     5        -1.40   bar -0.11  0.09  , metrics=EvalMetrics(trade_count=10, initial_market_value=100000.0, end_market_value=99873.7, total_pnl=-126.3, unrealized_pnl=-2.9132252166164108e-12, total_return_pct=-0.1263000000000014, annual_return_pct=None, total_profit=20.0, total_loss=-146.3, total_fees=0.0, max_drawdown=-130.3000000000029, max_drawdown_pct=-0.13030000000003872, win_rate=20.0, loss_rate=80.0, winning_trades=2, losing_trades=8, avg_pnl=-12.629999999999999, avg_return_pct=-1.076, avg_trade_bars=3.5, avg_profit=10.0, avg_profit_pct=1.3599999999999999, avg_winning_trade_bars=5.0, avg_loss=-18.2875, avg_loss_pct=-1.685, avg_losing_trade_bars=3.125, largest_win=11.0, largest_win_pct=1.52, largest_win_bars=5, largest_loss=-28.5, largest_loss_pct=-2.0, largest_loss_bars=2, max_wins=1, max_losses=4, sharpe=-0.15581113647095293, sortino=-0.09762036383519859, calmar=None, profit_factor=0.41337668369743363, ulcer_index=0.03270554702835016, upi=-0.033019579539121456, equity_r2=0.8884912984401415, std_error=43.417314435481934, annual_std_error=None, annual_volatility_pct=None), metrics_df=                      name         value\n", "0              trade_count  1.000000e+01\n", "1     initial_market_value  1.000000e+05\n", "2         end_market_value  9.987370e+04\n", "3                total_pnl -1.263000e+02\n", "4           unrealized_pnl -2.913225e-12\n", "5         total_return_pct -1.263000e-01\n", "6             total_profit  2.000000e+01\n", "7               total_loss -1.463000e+02\n", "8               total_fees  0.000000e+00\n", "9             max_drawdown -1.303000e+02\n", "10        max_drawdown_pct -1.303000e-01\n", "11                win_rate  2.000000e+01\n", "12               loss_rate  8.000000e+01\n", "13          winning_trades  2.000000e+00\n", "14           losing_trades  8.000000e+00\n", "15                 avg_pnl -1.263000e+01\n", "16          avg_return_pct -1.076000e+00\n", "17          avg_trade_bars  3.500000e+00\n", "18              avg_profit  1.000000e+01\n", "19          avg_profit_pct  1.360000e+00\n", "20  avg_winning_trade_bars  5.000000e+00\n", "21                avg_loss -1.828750e+01\n", "22            avg_loss_pct -1.685000e+00\n", "23   avg_losing_trade_bars  3.125000e+00\n", "24             largest_win  1.100000e+01\n", "25         largest_win_pct  1.520000e+00\n", "26        largest_win_bars  5.000000e+00\n", "27            largest_loss -2.850000e+01\n", "28        largest_loss_pct -2.000000e+00\n", "29       largest_loss_bars  2.000000e+00\n", "30                max_wins  1.000000e+00\n", "31              max_losses  4.000000e+00\n", "32                  sharpe -1.558111e-01\n", "33                 sortino -9.762036e-02\n", "34           profit_factor  4.133767e-01\n", "35             ulcer_index  3.270555e-02\n", "36                     upi -3.301958e-02\n", "37               equity_r2  8.884913e-01\n", "38               std_error  4.341731e+01, bootstrap=None, signals=None, stops=None)\n"]}], "source": ["from pybroker import Strategy\n", "from pybroker.ext.data import AKShare\n", "from pybroker import highest\n", "\n", "def exec_fn(ctx):\n", "    # 获取 10 日最高价的指标数据\n", "    high_10d = ctx.indicator('high_10d')\n", "    # 如果没有多头仓位，且当前价格突破 10 日最高价，则买入 100 股\n", "    if not ctx.long_pos() and high_10d[-1] > high_10d[-2]:\n", "        ctx.buy_shares = 100\n", "        # 持有 5 天\n", "        ctx.hold_bars = 5\n", "        # 设置 2% 的止损\n", "        ctx.stop_loss_pct = 2\n", "\n", "# 创建策略，使用 AKShare 数据源，指定开始和结束日期\n", "strategy = Strategy(AKShare(), start_date='1/1/2023', end_date='7/1/2023')\n", "# 添加执行函数，指定交易的股票代码和所需的指标\n", "strategy.add_execution(\n", "    exec_fn, \n", "    ['600000.SH', '000001.SZ'],  # 这里可以根据需要修改股票代码\n", "    indicators=highest('high_10d', 'close', period=10)\n", ")\n", "\n", "# 进行回测，前 20 天作为预热期\n", "result = strategy.backtest(warmup=20)\n", "\n", "# 打印回测结果\n", "print(result)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2023-01-01 00:00:00 to 2023-12-31 00:00:00\n", "\n", "Loading bar data...\n", "Loaded bar data: 0:00:00 \n", "\n", "Test split: 2023-01-03 00:00:00 to 2023-12-29 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 242) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "100% (242 of 242) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n", "TestResult(start_date=datetime.datetime(2023, 1, 1, 0, 0), end_date=datetime.datetime(2023, 12, 31, 0, 0), portfolio=                cash    equity  margin  market_value   pnl  unrealized_pnl  \\\n", "date                                                                         \n", "2023-01-03  100000.0  100000.0     0.0      100000.0   0.0             0.0   \n", "2023-01-04  100000.0  100000.0     0.0      100000.0   0.0             0.0   \n", "2023-01-05  100000.0  100000.0     0.0      100000.0   0.0             0.0   \n", "2023-01-06  100000.0  100000.0     0.0      100000.0   0.0             0.0   \n", "2023-01-09  100000.0  100000.0     0.0      100000.0   0.0             0.0   \n", "...              ...       ...     ...           ...   ...             ...   \n", "2023-12-25   99926.0   99926.0     0.0       99926.0 -74.0             0.0   \n", "2023-12-26   99926.0   99926.0     0.0       99926.0 -74.0             0.0   \n", "2023-12-27   99926.0   99926.0     0.0       99926.0 -74.0             0.0   \n", "2023-12-28   99926.0   99926.0     0.0       99926.0 -74.0             0.0   \n", "2023-12-29   99926.0   99926.0     0.0       99926.0 -74.0             0.0   \n", "\n", "            fees  \n", "date              \n", "2023-01-03   0.0  \n", "2023-01-04   0.0  \n", "2023-01-05   0.0  \n", "2023-01-06   0.0  \n", "2023-01-09   0.0  \n", "...          ...  \n", "2023-12-25   0.0  \n", "2023-12-26   0.0  \n", "2023-12-27   0.0  \n", "2023-12-28   0.0  \n", "2023-12-29   0.0  \n", "\n", "[242 rows x 7 columns], positions=                      long_shares  short_shares  close  equity  market_value  \\\n", "symbol    date                                                                 \n", "600000.SH 2023-03-06          100             0   7.33   733.0         733.0   \n", "          2023-03-07          100             0   7.31   731.0         731.0   \n", "          2023-03-08          100             0   7.31   731.0         731.0   \n", "          2023-03-09          100             0   7.27   727.0         727.0   \n", "          2023-03-10          100             0   7.15   715.0         715.0   \n", "...                           ...           ...    ...     ...           ...   \n", "          2023-11-23          100             0   6.93   693.0         693.0   \n", "          2023-11-24          100             0   6.95   695.0         695.0   \n", "          2023-11-27          100             0   6.90   690.0         690.0   \n", "          2023-11-28          100             0   6.87   687.0         687.0   \n", "          2023-11-29          100             0   6.82   682.0         682.0   \n", "\n", "                      margin  unrealized_pnl  \n", "symbol    date                                \n", "600000.SH 2023-03-06     0.0            -1.0  \n", "          2023-03-07     0.0            -3.0  \n", "          2023-03-08     0.0            -3.0  \n", "          2023-03-09     0.0            -7.0  \n", "          2023-03-10     0.0           -19.0  \n", "...                      ...             ...  \n", "          2023-11-23     0.0            -2.0  \n", "          2023-11-24     0.0             0.0  \n", "          2023-11-27     0.0            -5.0  \n", "          2023-11-28     0.0            -8.0  \n", "          2023-11-29     0.0           -13.0  \n", "\n", "[82 rows x 7 columns], orders=    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  600000.SH 2023-03-06     100          NaN        7.34   0.0\n", "2   sell  600000.SH 2023-03-14     100          NaN        7.04   0.0\n", "3    buy  600000.SH 2023-04-04     100          NaN        7.19   0.0\n", "4   sell  600000.SH 2023-05-22     100          NaN        7.56   0.0\n", "5    buy  600000.SH 2023-06-12     100          NaN        7.48   0.0\n", "6   sell  600000.SH 2023-06-21     100          NaN        7.32   0.0\n", "7    buy  600000.SH 2023-07-17     100          NaN        7.40   0.0\n", "8   sell  600000.SH 2023-07-26     100          NaN        7.30   0.0\n", "9    buy  600000.SH 2023-07-31     100          NaN        7.58   0.0\n", "10  sell  600000.SH 2023-08-14     100          NaN        7.20   0.0\n", "11   buy  600000.SH 2023-09-15     100          NaN        7.12   0.0\n", "12  sell  600000.SH 2023-10-11     100          NaN        7.04   0.0\n", "13   buy  600000.SH 2023-11-16     100          NaN        6.95   0.0\n", "14  sell  600000.SH 2023-11-30     100          NaN        6.86   0.0, trades=    type     symbol entry_date  exit_date  entry  exit  shares   pnl  \\\n", "id                                                                     \n", "1   long  600000.SH 2023-03-06 2023-03-14   7.34  7.04     100 -30.0   \n", "2   long  600000.SH 2023-04-04 2023-05-22   7.19  7.56     100  37.0   \n", "3   long  600000.SH 2023-06-12 2023-06-21   7.48  7.32     100 -16.0   \n", "4   long  600000.SH 2023-07-17 2023-07-26   7.40  7.30     100 -10.0   \n", "5   long  600000.SH 2023-07-31 2023-08-14   7.58  7.20     100 -38.0   \n", "6   long  600000.SH 2023-09-15 2023-10-11   7.12  7.04     100  -8.0   \n", "7   long  600000.SH 2023-11-16 2023-11-30   6.95  6.86     100  -9.0   \n", "\n", "    return_pct  agg_pnl  bars  pnl_per_bar  stop   mae   mfe  \n", "id                                                            \n", "1        -4.09    -30.0     6        -5.00  None -0.31  0.12  \n", "2         5.15      7.0    30         1.23  None -0.03  1.03  \n", "3        -2.14     -9.0     7        -2.29  None -0.19  0.06  \n", "4        -1.35    -19.0     7        -1.43  None -0.33  0.08  \n", "5        -5.01    -57.0    10        -3.80  loss -0.38  0.07  \n", "6        -1.12    -65.0    12        -0.67  None -0.12  0.08  \n", "7        -1.29    -74.0    10        -0.90  None -0.15  0.05  , metrics=EvalMetrics(trade_count=7, initial_market_value=100000.0, end_market_value=99926.0, total_pnl=-74.0, unrealized_pnl=0.0, total_return_pct=-0.07399999999999629, annual_return_pct=None, total_profit=37.0, total_loss=-111.0, total_fees=0.0, max_drawdown=-136.0, max_drawdown_pct=-0.13592116572388457, win_rate=14.285714285714285, loss_rate=85.71428571428571, winning_trades=1, losing_trades=6, avg_pnl=-10.571428571428571, avg_return_pct=-1.4071428571428568, avg_trade_bars=11.714285714285714, avg_profit=37.0, avg_profit_pct=5.15, avg_winning_trade_bars=30.0, avg_loss=-18.5, avg_loss_pct=-2.5, avg_losing_trade_bars=8.666666666666666, largest_win=37.0, largest_win_pct=5.15, largest_win_bars=30, largest_loss=-38.0, largest_loss_pct=-5.01, largest_loss_bars=10, max_wins=1, max_losses=5, sharpe=-0.06064913690839568, sortino=-0.05028378978241883, calmar=None, profit_factor=0.7516778523490766, ulcer_index=0.017349742783247316, upi=-0.017697033337265966, equity_r2=0.6831787526601392, std_error=31.568372147986434, annual_std_error=None, annual_volatility_pct=None), metrics_df=                      name          value\n", "0              trade_count       7.000000\n", "1     initial_market_value  100000.000000\n", "2         end_market_value   99926.000000\n", "3                total_pnl     -74.000000\n", "4           unrealized_pnl       0.000000\n", "5         total_return_pct      -0.074000\n", "6             total_profit      37.000000\n", "7               total_loss    -111.000000\n", "8               total_fees       0.000000\n", "9             max_drawdown    -136.000000\n", "10        max_drawdown_pct      -0.135921\n", "11                win_rate      14.285714\n", "12               loss_rate      85.714286\n", "13          winning_trades       1.000000\n", "14           losing_trades       6.000000\n", "15                 avg_pnl     -10.571429\n", "16          avg_return_pct      -1.407143\n", "17          avg_trade_bars      11.714286\n", "18              avg_profit      37.000000\n", "19          avg_profit_pct       5.150000\n", "20  avg_winning_trade_bars      30.000000\n", "21                avg_loss     -18.500000\n", "22            avg_loss_pct      -2.500000\n", "23   avg_losing_trade_bars       8.666667\n", "24             largest_win      37.000000\n", "25         largest_win_pct       5.150000\n", "26        largest_win_bars      30.000000\n", "27            largest_loss     -38.000000\n", "28        largest_loss_pct      -5.010000\n", "29       largest_loss_bars      10.000000\n", "30                max_wins       1.000000\n", "31              max_losses       5.000000\n", "32                  sharpe      -0.060649\n", "33                 sortino      -0.050284\n", "34           profit_factor       0.751678\n", "35             ulcer_index       0.017350\n", "36                     upi      -0.017697\n", "37               equity_r2       0.683179\n", "38               std_error      31.568372, bootstrap=None, signals=None, stops=None)\n"]}], "source": ["import talib\n", "from pybroker import Strategy\n", "from pybroker.ext.data import AKShare\n", "\n", "\n", "def exec_fn(ctx):\n", "    close_prices = ctx.close\n", "    # 使用 TA-Lib 计算 5 日简单移动平均线\n", "    sma_5 = talib.SMA(close_prices, timeperiod=5)\n", "    # 使用 TA-Lib 计算 20 日简单移动平均线\n", "    sma_20 = talib.SMA(close_prices, timeperiod=20)\n", "\n", "    # 检查是否发生黄金交叉\n", "    if not ctx.long_pos() and sma_5[-1] > sma_20[-1] and sma_5[-2] <= sma_20[-2]:\n", "        # 当发生黄金交叉且没有多头仓位时，买入 100 股\n", "        ctx.buy_shares = 100\n", "        # 设置 5% 的止损\n", "        ctx.stop_loss_pct = 5\n", "\n", "    # 检查是否发生死亡交叉（短期均线向下穿过长期均线）\n", "    elif ctx.long_pos() and sma_5[-1] < sma_20[-1] and sma_5[-2] >= sma_20[-2]:\n", "        # 当发生死亡交叉且持有多头仓位时，卖出所有股票\n", "        ctx.sell_all_shares()\n", "\n", "\n", "# 创建策略，使用 AKShare 作为数据源，设置回测的起始和结束日期\n", "strategy = Strategy(AKShare(), start_date='2023-01-01', end_date='2023-12-31')\n", "\n", "# 添加执行函数，指定交易的股票代码\n", "strategy.add_execution(\n", "    exec_fn,\n", "    ['600000.SH'],  # 这里可以根据需要修改股票代码\n", ")\n", "\n", "# 进行回测，设置 20 天的预热期\n", "result = strategy.backtest(warmup=20)\n", "\n", "# 打印回测结果\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["2021-03-01          NaN\n", "2021-03-01          NaN\n", "2021-03-02          NaN\n", "2021-03-02          NaN\n", "2021-03-03          NaN\n", "                ...    \n", "2023-02-27    48.687855\n", "2023-02-28    51.196717\n", "2023-02-28    48.697929\n", "2023-03-01    51.345340\n", "2023-03-01    48.728674\n", "Length: 974, dtype: float64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import talib\n", "import pybroker\n", "\n", "rsi_20 = pybroker.indicator('rsi_20', lambda data: talib.RSI(data.close, timeperiod=20))\n", "rsi_20(df)"]}], "metadata": {"kernelspec": {"display_name": "quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}