{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 指标的计算及在量化策略中的应用-Pybroker\n", "\n", "## 老师整理的资料\n", "\n", "pybroker框架的模块中文说明\n", "\n", "https://www.yuque.com/wangpeng-lucdx/kb/sm5aopgqv5g0fieg?singleDoc# 《pybroker使用说明》\n", "\n", "A股股票id\n", "\n", "https://www.yuque.com/wangpeng-lucdx/kb/kff5ugrp8dise9sm?singleDoc#9b55 《沪深股票代码大全》\n", "\n", "\n", "## 指数策略在pybroker中的实现\n", "\n", "https://www.pybroker.com/zh-cn/latest/notebooks/5.%20Writing%20Indicators.html"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["<diskcache.core.Cache at 0x20fffabc090>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pybroker\n", "from pybroker.ext.data import AKShare\n", "from pybroker import ExecContext, StrategyConfig, Strategy\n", "from pybroker.ext.data import AKShare\n", "import matplotlib.pyplot as plt\n", "\n", "akshare = AKShare()\n", "\n", "pybroker.enable_data_source_cache('akshare')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 突破策略中实际上引入了两个指标\n", "- **策略原理**：当股票价格突破近期的高点或者低点时，产生相应的买入或卖出信号。\n", "- **具体操作**：\n", "    - **买入条件**：观察一段时间（如10个交易日）内的股票最高价，当今日收盘价突破这10日的最高价时，以收盘价买入股票。同时，设置止损价位为买入价下跌10%，止盈价位为买入价上涨20%。\n", "    - **卖出条件**：当股票价格跌破止损价位或者达到止盈价位时，卖出股票。另外，若观察到一段时间内的最低价被突破（即收盘价低于过去10日最低价），也可考虑卖出股票，以控制风险。"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 17% (131 of 728) |###                   | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 34% (251 of 728) |#######               | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 52% (381 of 728) |###########           | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 63% (461 of 728) |#############         | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 82% (601 of 728) |##################    | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    high = ctx.high[-11:-1].max()\n", "    low = ctx.low[-11:-1].min()\n", "    #print(high, low)\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > high:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_loss_pct = 10\n", "        ctx.stop_profit_pct = 20\n", "    \n", "    if ctx.long_pos() and ctx.close[-1] < low:\n", "        ctx.sell_all_shares()\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### pybroker中的指标模块：indicator及在数据框中的使用\n", "\n", "pybroker.indicator模块聚焦于交易指标相关功能，包含指标类定义、指标计算函数、指标集合管理等内容，帮助开发者在算法交易策略中运用各类指标分析市场趋势、判断买卖信号。\n", "\n", "* 可以使用 IndicatorSet 来计算多个指标。通过将 cmma_20 和 hhv_5 指标添加到 IndicatorSet 中，可以一起计算它们。最终输出将是一个包含两者的 Pandas DataFrame ："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded cached bar data.\n", "\n"]}], "source": ["df = akshare.query(symbols='000001', start_date='20200101', end_date='20230830', adjust='')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 2) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 50% (1 of 2) |#############             | Elapsed Time: 0:00:04 ETA:   0:00:04\n", "100% (2 of 2) |##########################| Elapsed Time: 0:00:04 Time:  0:00:04\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>high_10d</th>\n", "      <th>low_10d</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001</td>\n", "      <td>2020-01-02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000001</td>\n", "      <td>2020-01-03</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000001</td>\n", "      <td>2020-01-06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000001</td>\n", "      <td>2020-01-07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000001</td>\n", "      <td>2020-01-08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>000001</td>\n", "      <td>2023-08-24</td>\n", "      <td>12.27</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>000001</td>\n", "      <td>2023-08-25</td>\n", "      <td>11.85</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>000001</td>\n", "      <td>2023-08-28</td>\n", "      <td>11.93</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>000001</td>\n", "      <td>2023-08-29</td>\n", "      <td>11.93</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>000001</td>\n", "      <td>2023-08-30</td>\n", "      <td>11.93</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>889 rows × 4 columns</p>\n", "</div>"], "text/plain": ["     symbol       date  high_10d  low_10d\n", "0    000001 2020-01-02       NaN      NaN\n", "1    000001 2020-01-03       NaN      NaN\n", "2    000001 2020-01-06       NaN      NaN\n", "3    000001 2020-01-07       NaN      NaN\n", "4    000001 2020-01-08       NaN      NaN\n", "..      ...        ...       ...      ...\n", "884  000001 2023-08-24     12.27    11.05\n", "885  000001 2023-08-25     11.85    11.05\n", "886  000001 2023-08-28     11.93    11.05\n", "887  000001 2023-08-29     11.93    11.05\n", "888  000001 2023-08-30     11.93    11.05\n", "\n", "[889 rows x 4 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from pybroker import IndicatorSet\n", "from pybroker.indicator import highest, lowest\n", "\n", "indicator_set = IndicatorSet()\n", "indicator_set.add(highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10))\n", "indicator_set(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 指标在策略中的使用\n", "\n", "*  high = ctx.indicator('high_10d')[-2]\n", "*  low = ctx.indicator('low_10d')[-2]\n", "*  indicators=[highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10)]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 4) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 25% (1 of 4) |######                    | Elapsed Time: 0:00:05 ETA:   0:00:17\n", "100% (4 of 4) |##########################| Elapsed Time: 0:00:05 Time:  0:00:05\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "  4% (31 of 728) |                       | Elapsed Time: 0:00:00 ETA:   0:00:03\n", " 22% (161 of 728) |####                  | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 28% (211 of 728) |######                | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 44% (321 of 728) |#########             | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 56% (411 of 728) |############          | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 77% (561 of 728) |################      | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 96% (701 of 728) |##################### | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:11\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    high = ctx.indicator('high_10d')[-2]\n", "    low = ctx.indicator('low_10d')[-2]\n", "    #print(high, low)\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > high:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_loss_pct = 10\n", "        ctx.stop_profit_pct = 20\n", "    \n", "    if ctx.long_pos() and ctx.close[-1] < low:\n", "        ctx.sell_all_shares()\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'],indicators=[highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10)])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-02-18</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>15.17</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-02-21</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>66.97</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-03-02</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>14.70</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-03-09</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>60.27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-04-01</td>\n", "      <td>779</td>\n", "      <td>NaN</td>\n", "      <td>58.84</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-11-14</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>11.99</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-11-21</td>\n", "      <td>341</td>\n", "      <td>NaN</td>\n", "      <td>249.76</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-02</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>264.02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-12-21</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>12.86</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-26</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>257.85</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>84 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  000001.SZ 2020-02-18    3253          NaN       15.17   0.0\n", "2    buy  002594.SZ 2020-02-21     744          NaN       66.97   0.0\n", "3   sell  000001.SZ 2020-03-02    3253          NaN       14.70   0.0\n", "4   sell  002594.SZ 2020-03-09     744          NaN       60.27   0.0\n", "5    buy  002594.SZ 2020-04-01     779          NaN       58.84   0.0\n", "..   ...        ...        ...     ...          ...         ...   ...\n", "80   buy  000001.SZ 2022-11-14    8075          NaN       11.99   0.0\n", "81  sell  002594.SZ 2022-11-21     341          NaN      249.76   0.0\n", "82   buy  002594.SZ 2022-12-02     322          NaN      264.02   0.0\n", "83  sell  000001.SZ 2022-12-21    8075          NaN       12.86   0.0\n", "84  sell  002594.SZ 2022-12-26     322          NaN      257.85   0.0\n", "\n", "[84 rows x 7 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["name     end_market_value\n", "value           187026.36\n", "Name: 2, dtype: object"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.loc[2]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 4) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 25% (1 of 4) |######                    | Elapsed Time: 0:00:00 ETA:   0:00:01\n", "100% (4 of 4) |##########################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 22% (161 of 728) |####                  | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 35% (261 of 728) |#######               | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 57% (421 of 728) |############          | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 82% (601 of 728) |##################    | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:01\n"]}], "source": ["\n", "\n", "def trying_strategy(ctx: ExecContext):\n", "    high = ctx.indicator('high_10d')[-2]\n", "    low = ctx.indicator('low_10d')[-2]\n", "    #print(high, low)\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > high:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_loss_pct = 10\n", "        ctx.stop_profit_pct = 20\n", "    \n", "    if ctx.long_pos() and ctx.close[-1] < low:\n", "        ctx.sell_all_shares()\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'],\n", "                       indicators=[highest('high_10d', 'high', period=10), lowest('low_10d', 'low', period=10)])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-02-18</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>15.17</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-02-21</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>66.97</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-03-02</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>14.70</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-03-09</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>60.27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-04-01</td>\n", "      <td>779</td>\n", "      <td>NaN</td>\n", "      <td>58.84</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-11-14</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>11.99</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-11-21</td>\n", "      <td>341</td>\n", "      <td>NaN</td>\n", "      <td>249.76</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-02</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>264.02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-12-21</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>12.86</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-26</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>257.85</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>84 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  000001.SZ 2020-02-18    3253          NaN       15.17   0.0\n", "2    buy  002594.SZ 2020-02-21     744          NaN       66.97   0.0\n", "3   sell  000001.SZ 2020-03-02    3253          NaN       14.70   0.0\n", "4   sell  002594.SZ 2020-03-09     744          NaN       60.27   0.0\n", "5    buy  002594.SZ 2020-04-01     779          NaN       58.84   0.0\n", "..   ...        ...        ...     ...          ...         ...   ...\n", "80   buy  000001.SZ 2022-11-14    8075          NaN       11.99   0.0\n", "81  sell  002594.SZ 2022-11-21     341          NaN      249.76   0.0\n", "82   buy  002594.SZ 2022-12-02     322          NaN      264.02   0.0\n", "83  sell  000001.SZ 2022-12-21    8075          NaN       12.86   0.0\n", "84  sell  002594.SZ 2022-12-26     322          NaN      257.85   0.0\n", "\n", "[84 rows x 7 columns]"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可定制的指标-pybroker向量化辅助函数\n", "\n", "PyBroker 库提供了向量化辅助函数，以简化计算指标的过程。其中一个辅助函数是 highv，它用于计算每个 n 条 Bar 周期的最高值。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["High prices: [  nan   nan 17.18 17.18 17.15 17.15 16.79 16.99 16.99 16.99 16.76 16.52\n", " 16.45 16.45 16.45 16.09 16.09 15.54 14.63 14.77 14.77 14.77 14.79 14.79\n", " 14.79 15.03 15.37 15.37 15.37 15.59 15.59 15.59 15.58 15.23 15.11 15.11\n", " 15.11 14.79 14.79 15.39 15.39 15.39 15.03 14.76 14.76 14.69 14.68 14.52\n", " 13.75 13.41 12.71 12.52 12.61 12.87 13.06 13.15 13.15 13.15 12.94 12.97\n", " 12.97 12.97 12.88 12.88 12.79 12.79 12.86 12.87 12.87 12.89 12.99 13.45\n", " 13.45 13.45 13.24 13.5  13.52 14.02 14.02 14.02 13.93 13.95 14.   14.\n", " 14.   13.79 13.63 13.3  13.36 13.51 13.51 13.51 13.4  13.04 13.04 13.07\n", " 13.07 13.32 13.55 13.55 13.57 13.59 13.62 13.67 13.67 13.67 13.49 13.08\n", " 12.99 12.89 12.89 12.85 12.8  12.8  12.8  12.8  12.8  13.12 13.43 14.25\n", " 15.68 15.68 15.76 15.76 15.76 15.53 14.89 14.89 14.68 14.27 14.73 14.73\n", " 14.73 14.49 14.41 14.01 13.5  13.54 13.54 13.54 13.59 14.04 14.04 14.04\n", " 13.9  13.95 14.13 14.38 14.38 14.47 15.19 15.19 15.19 15.15 15.1  14.59\n", " 14.6  14.6  14.6  15.13 15.13 15.14 15.32 15.32 15.32 14.96 15.43 15.43\n", " 15.43 15.34 15.34 15.35 15.44 15.57 16.07 16.07 16.07 15.86 15.63 15.63\n", " 15.31 15.31 15.31 15.18 15.9  16.06 16.06 16.56 17.1  17.48 17.54 17.91\n", " 17.91 18.13 18.13 18.13 17.76 17.77 17.77 17.77 17.96 18.32 18.32 18.32\n", " 17.84 18.11 18.11 18.11 17.81 17.66 17.83 18.46 18.85 18.86 19.62 19.62\n", " 19.62 19.5  19.7  19.74 20.05 20.05 20.05 19.63 19.54 19.3  18.91 18.71\n", " 18.71 18.88 18.88 19.01 19.01 19.01 18.95 18.36 18.28 18.26 18.26 18.85\n", " 19.17 19.2  19.34 19.34 19.34 19.56 19.9  19.9  20.38 21.   21.   21.\n", " 21.   22.7  22.7  22.7  22.47 22.47 22.49 22.49 23.08 23.08 23.09 24.55\n", " 24.55 24.95 24.95 24.95 24.93 24.93 24.8  24.58 24.3  24.3  23.85 22.38\n", " 22.16 22.16 22.09 21.65 23.01 23.01 23.01 22.92 22.34 21.6  21.27 21.48\n", " 21.6  21.66 21.66 21.73 21.73 21.73 21.55 21.55 21.23 21.14 21.49 21.93\n", " 22.01 22.01 22.01 21.78 21.68 21.68 21.64 21.56 21.3  20.78 20.78 20.67\n", " 21.15 21.69 23.01 23.01 23.29 23.29 23.29 23.35 23.59 23.59 23.59 24.05\n", " 24.05 24.05 23.86 23.55 23.55 23.6  23.9  23.9  23.9  23.82 23.82 24.6\n", " 25.01 25.01 25.01 24.79 24.5  24.2  23.92 24.54 24.54 24.54 24.65 24.65\n", " 24.65 24.2  23.37 23.26 23.26 23.1  22.97 23.14 23.14 23.36 23.36 23.36\n", " 22.78 23.2  23.2  23.2  22.78 22.78 22.78 22.55 21.51 21.27 21.16 21.62\n", " 21.62 21.62 21.34 21.21 20.6  20.45 20.38 20.1  18.8  17.96 17.96 18.01\n", " 18.01 18.01 17.89 17.86 19.06 19.73 19.81 19.89 19.89 19.95 19.95 20.62\n", " 20.62 20.62 20.34 19.42 19.36 19.36 19.16 18.46 18.39 17.88 18.4  18.4\n", " 18.45 19.24 19.24 19.24 20.57 20.57 20.57 20.21 19.52 19.52 19.08 18.53\n", " 17.98 17.68 17.97 18.15 18.15 18.64 19.4  19.4  19.58 19.58 19.66 19.66\n", " 19.66 19.57 20.   20.04 20.12 20.12 20.12 20.05 19.92 19.72 19.5  19.39\n", " 18.18 18.03 17.87 17.64 17.53 18.35 18.35 18.43 18.43 18.43 18.22 18.15\n", " 18.15 18.15 18.12 17.88 17.87 17.68 17.58 17.64 17.64 17.65 18.1  18.46\n", " 18.47 18.7  18.7  18.7  18.38 18.27 17.72 17.72 17.72 17.59 17.59 17.59\n", " 17.39 17.32 17.31 17.22 17.17 16.82 16.82 17.15 17.15 17.2  17.2  17.41\n", " 17.41 17.41 17.   16.98 16.52 16.52 17.33 17.35 17.35 17.35 17.2  16.85\n", " 16.65 16.39 16.83 16.86 16.99 17.1  17.1  17.1  16.58 16.42 16.77 16.77\n", " 16.77 16.51 16.23 16.19 15.91 15.92 15.92 15.92 15.71 15.71 15.33 14.72\n", " 14.57 14.9  14.9  14.9  14.49 14.45 14.7  14.7  15.18 15.18 15.2  15.2\n", " 15.2  14.98 15.21 15.38 15.75 16.39 16.39 16.4  16.4  16.4  16.05 16.04\n", " 16.42 16.42 16.42 15.9  15.85 16.06 16.06 16.06 15.65 15.65 15.65 15.65\n", " 15.32 15.32 14.96 14.63 14.63 14.63 14.61 14.71 14.75 14.75 15.02 15.02\n", " 15.02 14.83 14.4  14.39 14.19 14.18 14.16 14.16 14.17 14.17 14.17 14.35\n", " 14.35 14.35 14.32 14.74 14.74 14.74 14.45 14.45 14.37 14.37 14.23 14.49\n", " 14.63 14.64 14.98 14.98 14.98 14.94 14.94 14.91 14.54 14.54 14.54 14.46\n", " 14.42 13.97 13.39 13.42 13.42 13.42 13.39 13.01 12.91 12.91 12.91 12.88\n", " 12.88 12.68 12.42 12.26 12.31 12.31 12.31 12.2  12.35 12.38 12.38 12.38\n", " 12.4  12.4  12.57 12.57 12.57 12.5  12.62 12.62 12.62 12.61 12.75 12.75\n", " 12.75 12.61 12.57 12.57 12.5  12.72 12.95 12.95 13.   13.   13.   12.57\n", " 12.57 12.43 12.43 12.29 12.29 12.15 12.15 12.11 11.86 11.84 11.6  11.6\n", " 11.6  11.53 11.53 11.48 11.48 11.29 11.2  11.09 10.66 10.68 10.68 10.68\n", " 10.67 10.67 10.67 10.82 10.88 10.88 10.89 10.89 11.43 11.95 12.01 12.01\n", " 12.01 11.82 11.69 11.82 11.82 11.82 12.17 12.17 12.99 13.03 13.1  13.1\n", " 13.53 13.53 13.53 13.43 13.7  13.7  13.7  13.24 13.24 13.34 13.34 13.34\n", " 13.04 12.95 12.98 12.98 13.11 13.14 13.14 13.16 13.77 14.32 14.48 14.62\n", " 14.8  14.8  14.8  14.67 14.95 15.08 15.08 15.11 15.11 15.13 15.15 15.15\n", " 15.15 14.99 14.7  14.6  14.32 14.21 14.21 14.13 14.13 13.98 13.96 13.96\n", " 13.67 14.15 14.15 14.15 14.1  14.05 14.05 13.86 14.17 14.24 14.29 14.29\n", " 14.29 13.85 13.69 13.53 13.2  13.14 13.05 13.05 13.05 12.82 12.75 12.79\n", " 12.9  12.9  12.9  12.82 12.64 12.68 12.68 12.68 12.67 12.67 12.65 12.68\n", " 12.68 12.68 12.56 12.69 12.93 13.   13.   13.   12.85 12.75 12.5  12.28\n", " 12.28 12.55 12.73 12.91 13.32 13.32 13.32 13.16 12.85 12.85 12.83 12.83\n", " 12.62 12.49 12.49 12.38 12.38 12.21 12.1  12.1  12.1  11.98 11.87 11.93\n", " 11.93 11.93 11.94 12.12 12.12 12.12 11.88 11.79 11.77 11.63 11.63 11.63\n", " 11.45 11.36 11.35 11.3  11.3  11.3  11.49 11.49 11.49 11.4  11.34 11.24\n", " 11.21 11.24 11.43 11.46 11.46 11.46 11.31 11.32 11.34 11.34 11.72 11.72\n", " 11.75 12.26 12.32 12.32 12.32 12.33 12.33 12.33 12.3  12.17 12.17 12.17\n", " 12.16 11.89 11.73 11.73 11.73 11.57 11.56 11.37 11.37 11.25 11.52 11.52\n", " 11.52]\n", "Low prices: [  nan   nan 16.87 17.07 16.66 16.66 16.66 16.69 16.69 16.52 16.33 16.33\n", " 16.33 16.   16.   15.54 13.99 13.99 13.99 14.6  14.62 14.5  14.5  14.5\n", " 14.65 14.65 14.65 15.03 15.2  15.2  15.24 15.23 15.04 14.99 14.99 14.5\n", " 14.5  14.5  14.69 14.69 14.69 14.45 14.45 14.45 14.68 14.52 13.75 13.41\n", " 12.71 12.23 12.23 12.15 12.15 12.15 12.61 12.87 12.94 12.8  12.8  12.8\n", " 12.61 12.61 12.61 12.74 12.74 12.59 12.59 12.59 12.68 12.68 12.68 12.89\n", " 12.99 13.23 13.23 13.23 13.24 13.5  13.52 13.77 13.69 13.69 13.69 13.79\n", " 13.63 13.3  13.23 13.2  13.2  13.2  13.36 12.92 12.92 12.92 12.96 13.\n", " 13.   13.   13.   13.32 13.54 13.54 13.57 13.59 13.49 13.08 12.99 12.82\n", " 12.82 12.82 12.76 12.76 12.64 12.6  12.6  12.6  12.8  12.8  12.8  13.12\n", " 13.43 14.25 15.48 15.48 14.86 14.86 14.68 14.27 14.15 14.14 14.14 14.14\n", " 14.41 14.01 13.5  13.24 13.24 13.24 13.34 13.34 13.34 13.34 13.59 13.76\n", " 13.7  13.7  13.7  13.95 14.13 14.18 14.18 14.47 15.1  14.59 14.45 14.45\n", " 14.45 14.37 14.37 14.37 14.46 15.08 15.08 14.9  14.9  14.9  14.94 14.94\n", " 15.21 15.01 15.01 15.01 15.3  15.35 15.44 15.57 15.57 15.57 15.12 15.12\n", " 15.12 14.8  14.8  14.8  15.17 15.18 15.9  16.03 16.03 16.56 17.1  17.48\n", " 17.54 17.56 17.56 17.7  17.63 17.63 17.63 17.63 17.63 17.63 17.7  17.64\n", " 17.64 17.64 17.81 17.66 17.18 17.18 17.18 17.37 17.83 18.46 18.85 18.86\n", " 19.06 19.06 19.06 19.5  19.7  19.63 19.54 19.3  18.91 18.71 18.71 18.65\n", " 18.52 18.52 18.52 18.74 18.74 18.36 18.28 17.9  17.9  17.9  18.   18.04\n", " 18.04 18.85 19.17 18.6  18.17 18.17 18.17 19.56 19.85 19.85 20.38 20.17\n", " 20.17 20.17 21.   22.34 22.23 22.03 22.03 22.03 22.37 22.37 22.81 22.81\n", " 23.09 23.28 23.28 24.6  24.6  24.58 23.82 23.82 23.82 22.38 21.93 21.93\n", " 21.93 21.38 21.38 21.38 21.45 21.65 22.34 21.6  20.85 20.4  20.4  20.4\n", " 21.27 21.48 21.2  21.2  20.47 20.47 20.47 20.53 20.53 20.53 20.75 21.14\n", " 21.49 21.78 21.5  21.5  21.5  21.56 21.3  20.7  20.7  20.67 20.36 20.26\n", " 20.26 20.26 21.15 21.69 22.98 22.94 22.94 22.94 22.94 23.29 23.29 23.29\n", " 23.5  23.53 23.53 23.07 23.07 23.07 23.32 23.6  23.6  23.49 23.48 23.48\n", " 23.48 24.6  24.5  24.2  23.92 23.89 23.77 23.77 23.77 24.3  24.3  24.2\n", " 23.37 23.22 23.22 23.1  22.65 22.16 22.16 22.16 22.97 23.08 22.78 22.34\n", " 22.34 22.34 21.81 21.81 21.81 22.06 21.51 21.27 21.16 21.12 20.76 20.76\n", " 20.76 21.21 20.6  20.45 20.38 20.1  18.8  17.76 17.75 17.75 17.69 17.69\n", " 17.69 17.81 17.68 17.68 17.68 17.86 19.06 19.73 19.81 19.89 19.67 19.67\n", " 19.67 19.42 19.3  19.3  19.16 18.46 18.39 17.72 17.72 17.72 17.8  17.88\n", " 18.04 18.04 18.45 19.   19.   19.   19.37 19.37 19.08 18.53 17.98 17.68\n", " 17.35 17.35 17.35 17.57 17.93 17.93 17.93 18.64 19.35 19.21 19.21 19.21\n", " 19.29 19.24 19.24 19.24 20.   20.04 19.92 19.72 19.5  19.39 18.18 18.03\n", " 17.87 17.64 17.42 17.42 17.4  17.4  17.4  18.27 18.22 18.11 17.8  17.8\n", " 17.8  17.88 17.87 17.68 17.58 17.51 17.44 17.44 17.44 17.59 17.59 17.65\n", " 18.1  18.46 18.38 18.27 17.58 17.55 17.55 17.55 17.52 17.52 17.39 17.32\n", " 17.31 17.22 17.17 16.75 16.75 16.48 16.48 16.48 16.66 17.12 17.12 17.19\n", " 17.   16.98 16.33 16.22 16.22 16.22 16.5  16.5  17.2  16.85 16.65 16.3\n", " 15.83 15.83 15.83 16.39 16.83 16.86 16.58 16.28 16.28 16.28 16.41 16.42\n", " 16.23 16.19 15.91 15.9  15.75 15.75 15.68 15.68 15.33 14.72 14.31 13.84\n", " 13.84 13.84 14.49 13.68 13.68 13.68 14.31 14.45 14.59 14.59 15.   14.98\n", " 14.85 14.68 14.68 14.68 15.21 15.38 15.75 16.28 16.05 15.92 15.8  15.8\n", " 15.8  15.9  15.81 15.81 15.81 15.81 14.85 14.73 14.73 14.73 15.32 15.32\n", " 14.96 14.55 14.55 14.55 14.38 14.38 14.38 14.41 14.41 14.62 14.62 14.62\n", " 14.4  14.39 14.19 14.18 14.08 14.08 14.08 13.95 13.95 13.95 14.16 14.16\n", " 14.16 13.91 13.91 13.91 14.12 14.44 14.34 14.34 14.08 14.08 14.08 14.2\n", " 14.23 14.49 14.63 14.64 14.92 14.91 14.53 14.42 14.42 14.42 14.42 13.97\n", " 13.37 13.24 13.24 13.24 13.39 13.01 12.81 12.81 12.81 12.79 12.79 12.68\n", " 12.42 12.26 12.03 12.03 12.03 12.16 12.12 12.06 12.06 12.06 12.11 12.11\n", " 12.11 12.13 12.25 12.25 12.34 12.34 12.34 12.4  12.42 12.42 12.42 12.48\n", " 12.51 12.51 12.5  12.33 12.33 12.33 12.36 12.72 12.73 12.56 12.56 12.34\n", " 12.34 12.29 12.29 12.   12.   12.   11.86 11.84 11.47 11.47 11.47 11.34\n", " 11.34 11.34 11.48 11.29 11.2  11.09 10.62 10.62 10.62 10.65 10.42 10.34\n", " 10.34 10.34 10.44 10.44 10.44 10.82 10.85 10.85 10.87 10.87 11.43 11.82\n", " 11.69 11.59 11.46 11.46 11.46 11.77 11.77 11.77 11.81 11.81 12.99 12.9\n", " 12.9  12.9  13.15 13.15 13.15 13.11 13.11 13.11 13.1  13.1  13.04 12.76\n", " 12.76 12.76 12.89 12.77 12.77 12.77 13.03 13.03 13.03 13.16 13.77 14.32\n", " 14.48 14.44 14.44 14.44 14.67 14.67 14.95 14.97 14.97 15.09 15.09 14.99\n", " 14.7  14.6  14.32 14.   14.   14.   14.04 13.98 13.82 13.82 13.67 13.6\n", " 13.43 13.43 13.43 14.02 14.02 13.86 13.69 13.69 13.69 13.78 14.17 13.85\n", " 13.69 13.53 13.2  13.14 13.05 12.87 12.87 12.82 12.75 12.57 12.57 12.57\n", " 12.7  12.79 12.6  12.6  12.53 12.53 12.53 12.53 12.53 12.58 12.58 12.58\n", " 12.54 12.48 12.48 12.48 12.56 12.69 12.85 12.75 12.5  12.1  12.1  12.1\n", " 12.12 12.12 12.26 12.55 12.73 12.91 12.84 12.84 12.62 12.62 12.62 12.49\n", " 12.49 12.34 12.34 12.21 12.01 11.93 11.93 11.93 11.87 11.6  11.59 11.59\n", " 11.59 11.84 11.84 11.84 11.88 11.79 11.77 11.47 11.47 11.47 11.45 11.36\n", " 11.35 11.18 11.18 11.18 11.18 11.18 11.18 11.23 11.34 11.24 11.2  11.2\n", " 11.2  11.2  11.21 11.24 11.31 11.25 11.25 11.25 11.31 11.26 11.26 11.26\n", " 11.67 11.67 11.75 12.16 12.03 12.03 12.03 12.16 12.1  12.1  12.1  11.89\n", " 11.67 11.67 11.67 11.57 11.56 11.32 11.32 11.25 11.13 11.13 11.13 11.23\n", " 11.13]\n"]}], "source": ["from pybroker import highv, lowv\n", "import numpy as np\n", "\n", "# 模拟价格数据\n", "prices = np.array([10, 12, 11, 13, 14, 12, 11, 10])\n", "\n", "# 计算每 3 个周期的最高价和最低价\n", "lookback = 3\n", "high_prices = highv(df.close.values, lookback)\n", "low_prices = lowv(df.close.values, lookback)\n", "\n", "print(\"High prices:\", high_prices)\n", "print(\"Low prices:\", low_prices)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-01-02</td>\n", "      <td>000001</td>\n", "      <td>16.65</td>\n", "      <td>16.95</td>\n", "      <td>16.55</td>\n", "      <td>16.87</td>\n", "      <td>1530232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-01-03</td>\n", "      <td>000001</td>\n", "      <td>16.94</td>\n", "      <td>17.31</td>\n", "      <td>16.92</td>\n", "      <td>17.18</td>\n", "      <td>1116195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-01-06</td>\n", "      <td>000001</td>\n", "      <td>17.01</td>\n", "      <td>17.34</td>\n", "      <td>16.91</td>\n", "      <td>17.07</td>\n", "      <td>862084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-01-07</td>\n", "      <td>000001</td>\n", "      <td>17.13</td>\n", "      <td>17.28</td>\n", "      <td>16.95</td>\n", "      <td>17.15</td>\n", "      <td>728608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-01-08</td>\n", "      <td>000001</td>\n", "      <td>17.00</td>\n", "      <td>17.05</td>\n", "      <td>16.63</td>\n", "      <td>16.66</td>\n", "      <td>847824</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>2023-08-24</td>\n", "      <td>000001</td>\n", "      <td>11.29</td>\n", "      <td>11.32</td>\n", "      <td>11.05</td>\n", "      <td>11.13</td>\n", "      <td>1291271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>2023-08-25</td>\n", "      <td>000001</td>\n", "      <td>11.10</td>\n", "      <td>11.33</td>\n", "      <td>11.08</td>\n", "      <td>11.23</td>\n", "      <td>845350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>2023-08-28</td>\n", "      <td>000001</td>\n", "      <td>11.84</td>\n", "      <td>11.93</td>\n", "      <td>11.49</td>\n", "      <td>11.52</td>\n", "      <td>1997610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>2023-08-29</td>\n", "      <td>000001</td>\n", "      <td>11.47</td>\n", "      <td>11.49</td>\n", "      <td>11.13</td>\n", "      <td>11.31</td>\n", "      <td>1964961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>2023-08-30</td>\n", "      <td>000001</td>\n", "      <td>11.27</td>\n", "      <td>11.27</td>\n", "      <td>11.13</td>\n", "      <td>11.13</td>\n", "      <td>1375638</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>889 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          date  symbol   open   high    low  close   volume\n", "0   2020-01-02  000001  16.65  16.95  16.55  16.87  1530232\n", "1   2020-01-03  000001  16.94  17.31  16.92  17.18  1116195\n", "2   2020-01-06  000001  17.01  17.34  16.91  17.07   862084\n", "3   2020-01-07  000001  17.13  17.28  16.95  17.15   728608\n", "4   2020-01-08  000001  17.00  17.05  16.63  16.66   847824\n", "..         ...     ...    ...    ...    ...    ...      ...\n", "884 2023-08-24  000001  11.29  11.32  11.05  11.13  1291271\n", "885 2023-08-25  000001  11.10  11.33  11.08  11.23   845350\n", "886 2023-08-28  000001  11.84  11.93  11.49  11.52  1997610\n", "887 2023-08-29  000001  11.47  11.49  11.13  11.31  1964961\n", "888 2023-08-30  000001  11.27  11.27  11.13  11.13  1375638\n", "\n", "[889 rows x 7 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["2020-01-02      NaN\n", "2020-01-03      NaN\n", "2020-01-06      NaN\n", "2020-01-07      NaN\n", "2020-01-08      NaN\n", "              ...  \n", "2023-08-24    12.27\n", "2023-08-25    11.85\n", "2023-08-28    11.93\n", "2023-08-29    11.93\n", "2023-08-30    11.93\n", "Length: 889, dtype: float64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["def hhv(bar_data, period):\n", "    return highv(bar_data.high, period)\n", "\n", "hhv_10 = pybroker.indicator('hhv_10', hhv, period=10)\n", "hhv_10(df)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["2020-01-02      NaN\n", "2020-01-03      NaN\n", "2020-01-06      NaN\n", "2020-01-07      NaN\n", "2020-01-08      NaN\n", "              ...  \n", "2023-08-24    11.05\n", "2023-08-25    11.05\n", "2023-08-28    11.05\n", "2023-08-29    11.05\n", "2023-08-30    11.05\n", "Length: 889, dtype: float64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def lwv(bar_data, period):\n", "    return lowv(bar_data.low, period)\n", "\n", "lwv_10 = pybroker.indicator('lwv_10', lwv, period=10)\n", "lwv_10(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 2) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 50% (1 of 2) |#############             | Elapsed Time: 0:00:04 ETA:   0:00:04\n", "100% (2 of 2) |##########################| Elapsed Time: 0:00:04 Time:  0:00:04\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>hhv_10</th>\n", "      <th>lwv_10</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001</td>\n", "      <td>2020-01-02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000001</td>\n", "      <td>2020-01-03</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000001</td>\n", "      <td>2020-01-06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000001</td>\n", "      <td>2020-01-07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000001</td>\n", "      <td>2020-01-08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>000001</td>\n", "      <td>2023-08-24</td>\n", "      <td>12.27</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>000001</td>\n", "      <td>2023-08-25</td>\n", "      <td>11.85</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>000001</td>\n", "      <td>2023-08-28</td>\n", "      <td>11.93</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>000001</td>\n", "      <td>2023-08-29</td>\n", "      <td>11.93</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>000001</td>\n", "      <td>2023-08-30</td>\n", "      <td>11.93</td>\n", "      <td>11.05</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>889 rows × 4 columns</p>\n", "</div>"], "text/plain": ["     symbol       date  hhv_10  lwv_10\n", "0    000001 2020-01-02     NaN     NaN\n", "1    000001 2020-01-03     NaN     NaN\n", "2    000001 2020-01-06     NaN     NaN\n", "3    000001 2020-01-07     NaN     NaN\n", "4    000001 2020-01-08     NaN     NaN\n", "..      ...        ...     ...     ...\n", "884  000001 2023-08-24   12.27   11.05\n", "885  000001 2023-08-25   11.85   11.05\n", "886  000001 2023-08-28   11.93   11.05\n", "887  000001 2023-08-29   11.93   11.05\n", "888  000001 2023-08-30   11.93   11.05\n", "\n", "[889 rows x 4 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from pybroker import IndicatorSet\n", "\n", "indicator_set = IndicatorSet()\n", "indicator_set.add(hhv_10, lwv_10)\n", "indicator_set(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 2) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 50% (1 of 2) |#############             | Elapsed Time: 0:00:04 ETA:   0:00:04\n", "100% (2 of 2) |##########################| Elapsed Time: 0:00:04 Time:  0:00:04\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>high_10d</th>\n", "      <th>low_10d</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001</td>\n", "      <td>2020-01-02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000001</td>\n", "      <td>2020-01-03</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000001</td>\n", "      <td>2020-01-06</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000001</td>\n", "      <td>2020-01-07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000001</td>\n", "      <td>2020-01-08</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>723</th>\n", "      <td>000001</td>\n", "      <td>2022-12-26</td>\n", "      <td>13.47</td>\n", "      <td>12.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>724</th>\n", "      <td>000001</td>\n", "      <td>2022-12-27</td>\n", "      <td>13.47</td>\n", "      <td>12.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>725</th>\n", "      <td>000001</td>\n", "      <td>2022-12-28</td>\n", "      <td>13.47</td>\n", "      <td>12.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>726</th>\n", "      <td>000001</td>\n", "      <td>2022-12-29</td>\n", "      <td>13.47</td>\n", "      <td>12.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>727</th>\n", "      <td>000001</td>\n", "      <td>2022-12-30</td>\n", "      <td>13.47</td>\n", "      <td>12.67</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>728 rows × 4 columns</p>\n", "</div>"], "text/plain": ["     symbol       date  high_10d  low_10d\n", "0    000001 2020-01-02       NaN      NaN\n", "1    000001 2020-01-03       NaN      NaN\n", "2    000001 2020-01-06       NaN      NaN\n", "3    000001 2020-01-07       NaN      NaN\n", "4    000001 2020-01-08       NaN      NaN\n", "..      ...        ...       ...      ...\n", "723  000001 2022-12-26     13.47    12.67\n", "724  000001 2022-12-27     13.47    12.67\n", "725  000001 2022-12-28     13.47    12.67\n", "726  000001 2022-12-29     13.47    12.67\n", "727  000001 2022-12-30     13.47    12.67\n", "\n", "[728 rows x 4 columns]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 策略中使用向量化辅助函数"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 4) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 25% (1 of 4) |######                    | Elapsed Time: 0:00:04 ETA:   0:00:14\n", "100% (4 of 4) |##########################| Elapsed Time: 0:00:04 Time:  0:00:04\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "  9% (71 of 728) |##                     | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 23% (171 of 728) |#####                 | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 35% (261 of 728) |#######               | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 55% (401 of 728) |############          | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 77% (561 of 728) |################      | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 97% (711 of 728) |##################### | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:05\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    high = ctx.indicator('hhv_10')[-2]\n", "    low = ctx.indicator('lwv_10')[-2]\n", "    #print(high, low)\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > high:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_loss_pct = 10\n", "        ctx.stop_profit_pct = 20\n", "    \n", "    if ctx.long_pos() and ctx.close[-1] < low:\n", "        ctx.sell_all_shares()\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'],indicators=[hhv_10, lwv_10])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-02-18</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>15.17</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-02-21</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>66.97</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-03-02</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>14.70</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-03-09</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>60.27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-04-01</td>\n", "      <td>779</td>\n", "      <td>NaN</td>\n", "      <td>58.84</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-11-14</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>11.99</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-11-21</td>\n", "      <td>341</td>\n", "      <td>NaN</td>\n", "      <td>249.76</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-02</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>264.02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-12-21</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>12.86</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-26</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>257.85</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>84 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  000001.SZ 2020-02-18    3253          NaN       15.17   0.0\n", "2    buy  002594.SZ 2020-02-21     744          NaN       66.97   0.0\n", "3   sell  000001.SZ 2020-03-02    3253          NaN       14.70   0.0\n", "4   sell  002594.SZ 2020-03-09     744          NaN       60.27   0.0\n", "5    buy  002594.SZ 2020-04-01     779          NaN       58.84   0.0\n", "..   ...        ...        ...     ...          ...         ...   ...\n", "80   buy  000001.SZ 2022-11-14    8075          NaN       11.99   0.0\n", "81  sell  002594.SZ 2022-11-21     341          NaN      249.76   0.0\n", "82   buy  002594.SZ 2022-12-02     322          NaN      264.02   0.0\n", "83  sell  000001.SZ 2022-12-21    8075          NaN       12.86   0.0\n", "84  sell  002594.SZ 2022-12-26     322          NaN      257.85   0.0\n", "\n", "[84 rows x 7 columns]"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## talib指标在pybroker策略中的实现\n", "\n", "* talib与pybroker.indicator指标的区别\n", "\n", "talib中的指标似乎比较传统基础，并且数量较多，数百个\n", "\n", "pybroker中的指标比较新颖，量少\n", "\n", "### 双均线策略-所谓的黄金交叉\n", "- **策略原理**：计算短期均线和长期均线，当短期均线上穿长期均线时，产生买入信号；当短期均线下穿长期均线时，产生卖出信号。同时，设置止损和止盈价位。\n", "- **具体操作**：\n", "    - **买入条件**：假设短期均线为 5 日均线，长期均线为 20 日均线。当 5 日均线的值大于 20 日均线的值，且当日收盘价大于 5 日均线和 20 日均线时，以当日收盘价买入股票。\n", "    - **卖出条件**：当 5 日均线的值小于 20 日均线的值，或者股票价格达到止盈价位（如买入后上涨 20%），或者股票价格达到止损价位（如买入后下跌 10%），则卖出股票。"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import talib\n", "\n", "fig=plt.figure(figsize=(18,6))\n", "talib.SMA(df.close, timeperiod=10).plot()\n", "talib.SMA(df.close, timeperiod=30).plot()\n", "df.close.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### p<PERSON><PERSON><PERSON><PERSON>中的交叉函数cross\n", "\n", "cross(a: ndarray[Any, dtype[float64]], b: ndarray[Any, dtype[float64]]) → ndarray[Any, dtype[bool_]]\n", "\n", "详细的指标意义：检查数组 a 是否上穿数组 b。当 a 上穿 b 时，返回的数组对应位置的值为 1（True），否则为 0（False）。\n", "\n", "参数：\n", "● a – 数据的 NumPy 数组。\n", "● b – 数据的 NumPy 数组。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n", "       0, 0, 0, 0, 0, 0, 0, 0, 0], dtype=int64)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import talib\n", "from pybroker.vect import cross\n", "\n", "cross(talib.SMA(df.close, timeperiod=10).values, talib.SMA(df.close, timeperiod=30).values)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["if 0:\n", "    print(2)\n", "else:\n", "    print(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### talib指标在pybroker策略的使用"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2022-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 4) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 25% (1 of 4) |######                    | Elapsed Time: 0:00:02 ETA:   0:00:06\n", "100% (4 of 4) |##########################| Elapsed Time: 0:00:02 Time:  0:00:02\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Test split: 2022-01-04 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 242) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 54% (131 of 242) |###########           | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (242 of 242) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["买入信号 2022-03-02 00:00:00 1\n", "卖出信号 2022-03-08 00:00:00 1\n", "买入信号 2022-03-29 00:00:00 1\n", "买入信号 2022-04-06 00:00:00 1\n", "卖出信号 2022-04-28 00:00:00 1\n", "卖出信号 2022-05-05 00:00:00 1\n", "买入信号 2022-05-11 00:00:00 1\n", "买入信号 2022-06-27 00:00:00 1\n", "卖出信号 2022-07-12 00:00:00 1\n", "卖出信号 2022-07-15 00:00:00 1\n", "买入信号 2022-08-31 00:00:00 1\n", "卖出信号 2022-09-27 00:00:00 1\n", "买入信号 2022-11-09 00:00:00 1\n", "买入信号 2022-11-16 00:00:00 1\n", "卖出信号 2022-11-22 00:00:00 1\n", "买入信号 2022-12-09 00:00:00 1\n", "卖出信号 2022-12-29 00:00:00 1\n", "\n", "Finished backtest: 0:00:05\n"]}], "source": ["# 定义短期均线指标\n", "sma_10 = pybroker.indicator('sma_10', lambda data: talib.SMA(data.close, timeperiod=10))\n", "# 定义长期均线指标\n", "sma_30 = pybroker.indicator('sma_30', lambda data: talib.SMA(data.close, timeperiod=30))\n", "\n", "def double_ma_strategy(ctx: ExecContext):\n", "    # 获取短期均线数据\n", "    sma_10_values = ctx.indicator('sma_10')\n", "    # 获取长期均线数据\n", "    sma_30_values = ctx.indicator('sma_30')\n", "\n", "    # 检查短期均线是否上穿长期均线（产生买入信号）\n", "    buy_signal = cross(sma_10_values, sma_30_values)[-1]\n", "    # 检查短期均线是否下穿长期均线（产生卖出信号）\n", "    sell_signal = cross(sma_30_values, sma_10_values)[-1]\n", "\n", "    if buy_signal and not ctx.long_pos():\n", "        # 计算购买的股票数量，这里使用全部资金购买\n", "        target_size = 1\n", "        shares = ctx.calc_target_shares(target_size)\n", "        ctx.buy_shares = shares\n", "        print('买入信号',ctx.dt,buy_signal)\n", "\n", "    if sell_signal and ctx.long_pos():\n", "        ctx.sell_all_shares()\n", "        print('卖出信号',ctx.dt,sell_signal)\n", "\n", "strategy = Strategy(data_source=akshare, start_date='2022-01-01', end_date='2023-01-01')\n", "strategy.add_execution(double_ma_strategy, ['000001.SZ','002594.SZ'],indicators=[sma_10, sma_30])\n", "result = strategy.backtest(warmup=30)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-03-03</td>\n", "      <td>402</td>\n", "      <td>NaN</td>\n", "      <td>247.43</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-03-09</td>\n", "      <td>402</td>\n", "      <td>NaN</td>\n", "      <td>216.80</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-03-30</td>\n", "      <td>379</td>\n", "      <td>NaN</td>\n", "      <td>229.62</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-04-07</td>\n", "      <td>40</td>\n", "      <td>NaN</td>\n", "      <td>16.43</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-04-29</td>\n", "      <td>379</td>\n", "      <td>NaN</td>\n", "      <td>237.29</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-05-06</td>\n", "      <td>40</td>\n", "      <td>NaN</td>\n", "      <td>15.06</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-05-12</td>\n", "      <td>357</td>\n", "      <td>NaN</td>\n", "      <td>253.20</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-06-28</td>\n", "      <td>10</td>\n", "      <td>NaN</td>\n", "      <td>14.54</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-07-13</td>\n", "      <td>357</td>\n", "      <td>NaN</td>\n", "      <td>305.30</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-07-18</td>\n", "      <td>10</td>\n", "      <td>NaN</td>\n", "      <td>13.32</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-09-01</td>\n", "      <td>8558</td>\n", "      <td>NaN</td>\n", "      <td>12.68</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-09-28</td>\n", "      <td>8558</td>\n", "      <td>NaN</td>\n", "      <td>12.08</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-11-10</td>\n", "      <td>385</td>\n", "      <td>NaN</td>\n", "      <td>260.38</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-11-17</td>\n", "      <td>319</td>\n", "      <td>NaN</td>\n", "      <td>11.71</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-11-23</td>\n", "      <td>385</td>\n", "      <td>NaN</td>\n", "      <td>249.35</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-12</td>\n", "      <td>354</td>\n", "      <td>NaN</td>\n", "      <td>270.74</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-30</td>\n", "      <td>354</td>\n", "      <td>NaN</td>\n", "      <td>258.44</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  002594.SZ 2022-03-03     402          NaN      247.43   0.0\n", "2   sell  002594.SZ 2022-03-09     402          NaN      216.80   0.0\n", "3    buy  002594.SZ 2022-03-30     379          NaN      229.62   0.0\n", "4    buy  000001.SZ 2022-04-07      40          NaN       16.43   0.0\n", "5   sell  002594.SZ 2022-04-29     379          NaN      237.29   0.0\n", "6   sell  000001.SZ 2022-05-06      40          NaN       15.06   0.0\n", "7    buy  002594.SZ 2022-05-12     357          NaN      253.20   0.0\n", "8    buy  000001.SZ 2022-06-28      10          NaN       14.54   0.0\n", "9   sell  002594.SZ 2022-07-13     357          NaN      305.30   0.0\n", "10  sell  000001.SZ 2022-07-18      10          NaN       13.32   0.0\n", "11   buy  000001.SZ 2022-09-01    8558          NaN       12.68   0.0\n", "12  sell  000001.SZ 2022-09-28    8558          NaN       12.08   0.0\n", "13   buy  002594.SZ 2022-11-10     385          NaN      260.38   0.0\n", "14   buy  000001.SZ 2022-11-17     319          NaN       11.71   0.0\n", "15  sell  002594.SZ 2022-11-23     385          NaN      249.35   0.0\n", "16   buy  002594.SZ 2022-12-12     354          NaN      270.74   0.0\n", "17  sell  002594.SZ 2022-12-30     354          NaN      258.44   0.0"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MACD\n", "\n", "https://zhuanlan.zhihu.com/p/348987788\n", "\n", "* 指数移动平均\n", "\n", "EMA(t)=平滑常数\\*当前价格+(1-平滑常数)*EMA(t-1)\n", "\n", "* 指数平滑异同移动平均线（moving average convergence-divergence, MACD）。MACD是以三个指数移动平均为基础，以两条曲线的形式出现在图表中，其两条线的交叉点，是一种交易信号。\n", "\n", "最初的MACD指标由两条线组成：一条实线（叫作MACD线）和一条虚线（叫作信号线）。MACD线由两个指数移动平均（EMA）计算而来，其对价格的反应相对较快。信号线是以MACD线为基础，通过对MACD线以EMA的方式进行运算，实现对MACD线的平滑，其对价格变动的反应相对较慢。在阿佩尔最初的体系中，较快的MACD线穿过较慢的信号线上升或者下降，为买入或者卖出的信号。大多数技术分析软件都提供MACD指标。\n", "\n", "手工做出MACD指标的步骤如下：\n", "\n", "（1）计算12日收盘价的EMA；\n", "\n", "（2）计算26日收盘价的EMA；\n", "\n", "（3）用12日收盘价的EMA减去26日收盘价的EMA，将其差值画成一条实线，这就是较快的MACD线；也叫离差值（DIF）\n", "\n", "（4）计算这条实线的9日EMA，将其结果画成一条虚线，这就是较慢的信号线。也叫离差平均值（DEA）。\n", "\n", "（5）（DIF-DEA）×2即为MACD值。\n", "\n", "每一个价格都反映了所有的市场参与者那一刻对股票价值的共识。移动平均值反映了一段时间内的市场价值共识的平均水平——它像是所有的市场参与者共识的连环照。较长的移动平均反映的是较长时间周期内市场价值共识的平均水平，而较短的移动平均则反映了较短时间周期内市场价值共识的平均水平。\n", "\n", "MACD线和信号线的交点表明了市场中空方和多方实力变换的平衡点。较快的MACD线反映的是短期内大众的心理变化，而较慢的信号线则反映了大众心理在较长期的变化。当较快的MACD线上升超过信号线时，表示多方主导了市场，这时候最好做多方；当较快的线落到较慢的信号线下面时，表示空方主导了市场，做空方比较有利。\n", "\n", "* MACD柱状线\n", "\n", "相比原始的MACD线，MACD柱状线能够提供更深刻的关于多空力量均衡的信息。它不仅能分辨出哪种力量处于主导地位，而且能够分辨其力量是在逐渐增强还是在减弱。MACD柱状线是技术分析师最好用的工具之一。\n", "\n", "MACD柱状线=MACD线-信号线\n", "\n", "MACD柱状线测量的是MACD线和信号线之间的差值。它将差值画为一根根柱状线——为一系列垂直的线条。\n", "\n", "MACD柱状线的斜率方向揭示了市场中的主导力量。向上倾斜的MACD柱状线表示多方的力量在增强，而向下倾斜的MACD柱状线则意味着空方的力量在增强。\n", "\n", "\n", "* MACD简单策略：\n", "\n", "快线DIF与慢线DEA都大于零，快线向上突破慢线，则买入\n", "\n", "快线DIF与慢线DEA都小于零，快线向下突破慢线，则卖出"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-01-02</td>\n", "      <td>000001</td>\n", "      <td>16.65</td>\n", "      <td>16.95</td>\n", "      <td>16.55</td>\n", "      <td>16.87</td>\n", "      <td>1530232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-01-03</td>\n", "      <td>000001</td>\n", "      <td>16.94</td>\n", "      <td>17.31</td>\n", "      <td>16.92</td>\n", "      <td>17.18</td>\n", "      <td>1116195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-01-06</td>\n", "      <td>000001</td>\n", "      <td>17.01</td>\n", "      <td>17.34</td>\n", "      <td>16.91</td>\n", "      <td>17.07</td>\n", "      <td>862084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-01-07</td>\n", "      <td>000001</td>\n", "      <td>17.13</td>\n", "      <td>17.28</td>\n", "      <td>16.95</td>\n", "      <td>17.15</td>\n", "      <td>728608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-01-08</td>\n", "      <td>000001</td>\n", "      <td>17.00</td>\n", "      <td>17.05</td>\n", "      <td>16.63</td>\n", "      <td>16.66</td>\n", "      <td>847824</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>2023-08-24</td>\n", "      <td>000001</td>\n", "      <td>11.29</td>\n", "      <td>11.32</td>\n", "      <td>11.05</td>\n", "      <td>11.13</td>\n", "      <td>1291271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>2023-08-25</td>\n", "      <td>000001</td>\n", "      <td>11.10</td>\n", "      <td>11.33</td>\n", "      <td>11.08</td>\n", "      <td>11.23</td>\n", "      <td>845350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>2023-08-28</td>\n", "      <td>000001</td>\n", "      <td>11.84</td>\n", "      <td>11.93</td>\n", "      <td>11.49</td>\n", "      <td>11.52</td>\n", "      <td>1997610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>2023-08-29</td>\n", "      <td>000001</td>\n", "      <td>11.47</td>\n", "      <td>11.49</td>\n", "      <td>11.13</td>\n", "      <td>11.31</td>\n", "      <td>1964961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>2023-08-30</td>\n", "      <td>000001</td>\n", "      <td>11.27</td>\n", "      <td>11.27</td>\n", "      <td>11.13</td>\n", "      <td>11.13</td>\n", "      <td>1375638</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>889 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          date  symbol   open   high    low  close   volume\n", "0   2020-01-02  000001  16.65  16.95  16.55  16.87  1530232\n", "1   2020-01-03  000001  16.94  17.31  16.92  17.18  1116195\n", "2   2020-01-06  000001  17.01  17.34  16.91  17.07   862084\n", "3   2020-01-07  000001  17.13  17.28  16.95  17.15   728608\n", "4   2020-01-08  000001  17.00  17.05  16.63  16.66   847824\n", "..         ...     ...    ...    ...    ...    ...      ...\n", "884 2023-08-24  000001  11.29  11.32  11.05  11.13  1291271\n", "885 2023-08-25  000001  11.10  11.33  11.08  11.23   845350\n", "886 2023-08-28  000001  11.84  11.93  11.49  11.52  1997610\n", "887 2023-08-29  000001  11.47  11.49  11.13  11.31  1964961\n", "888 2023-08-30  000001  11.27  11.27  11.13  11.13  1375638\n", "\n", "[889 rows x 7 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import riskfolio as rp"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["x,y,z=talib.MACD(df.close,fastperiod=12, slowperiod=26, signalperiod=9)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["from pyecharts import options as opts\n", "from pyecharts.commons.utils import JsCode\n", "from pyecharts.charts import Kline, Line, Bar, Grid"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["0           NaN\n", "1           NaN\n", "2           NaN\n", "3           NaN\n", "4           NaN\n", "         ...   \n", "884   -0.115714\n", "885   -0.131597\n", "886   -0.119406\n", "887   -0.125247\n", "888   -0.142755\n", "Length: 889, dtype: float64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"5f71318954754d7091efe4bcbabe5d8b\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts'], function(echarts) {\n", "                var chart_5f71318954754d7091efe4bcbabe5d8b = echarts.init(\n", "                    document.getElementById('5f71318954754d7091efe4bcbabe5d8b'), 'white', {renderer: 'canvas'});\n", "                var option_5f71318954754d7091efe4bcbabe5d8b = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"line\",\n", "            \"name\": \"DIF\",\n", "            \"connectNulls\": false,\n", "            \"xAxisIndex\": 0,\n", "            \"symbolSize\": 4,\n", "            \"showSymbol\": true,\n", "            \"smooth\": false,\n", "            \"clip\": true,\n", "            \"step\": false,\n", "            \"stackStrategy\": \"samesign\",\n", "            \"data\": [\n", "                [\n", "                    0,\n", "                    null\n", "                ],\n", "                [\n", "                    1,\n", "                    null\n", "                ],\n", "                [\n", "                    2,\n", "                    null\n", "                ],\n", "                [\n", "                    3,\n", "                    null\n", "                ],\n", "                [\n", "                    4,\n", "                    null\n", "                ],\n", "                [\n", "                    5,\n", "                    null\n", "                ],\n", "                [\n", "                    6,\n", "                    null\n", "                ],\n", "                [\n", "                    7,\n", "                    null\n", "                ],\n", "                [\n", "                    8,\n", "                    null\n", "                ],\n", "                [\n", "                    9,\n", "                    null\n", "                ],\n", "                [\n", "                    10,\n", "                    null\n", "                ],\n", "                [\n", "                    11,\n", "                    null\n", "                ],\n", "                [\n", "                    12,\n", "                    null\n", "                ],\n", "                [\n", "                    13,\n", "                    null\n", "                ],\n", "                [\n", "                    14,\n", "                    null\n", "                ],\n", "                [\n", "                    15,\n", "                    null\n", "                ],\n", "                [\n", "                    16,\n", "                    null\n", "                ],\n", "                [\n", "                    17,\n", "                    null\n", "                ],\n", "                [\n", "                    18,\n", "                    null\n", "                ],\n", "                [\n", "                    19,\n", "                    null\n", "                ],\n", "                [\n", "                    20,\n", "                    null\n", "                ],\n", "                [\n", "                    21,\n", "                    null\n", "                ],\n", "                [\n", "                    22,\n", "                    null\n", "                ],\n", "                [\n", "                    23,\n", "                    null\n", "                ],\n", "                [\n", "                    24,\n", "                    null\n", "                ],\n", "                [\n", "                    25,\n", "                    null\n", "                ],\n", "                [\n", "                    26,\n", "                    null\n", "                ],\n", "                [\n", "                    27,\n", "                    null\n", "                ],\n", "                [\n", "                    28,\n", "                    null\n", "                ],\n", "                [\n", "                    29,\n", "                    null\n", "                ],\n", "                [\n", "                    30,\n", "                    null\n", "                ],\n", "                [\n", "                    31,\n", "                    null\n", "                ],\n", "                [\n", "                    32,\n", "                    null\n", "                ],\n", "                [\n", "                    33,\n", "                    -0.44353958115601877\n", "                ],\n", "                [\n", "                    34,\n", "                    -0.41230765798455593\n", "                ],\n", "                [\n", "                    35,\n", "                    -0.4318005084002525\n", "                ],\n", "                [\n", "                    36,\n", "                    -0.4190179780034313\n", "                ],\n", "                [\n", "                    37,\n", "                    -0.40981208691756343\n", "                ],\n", "                [\n", "                    38,\n", "                    -0.4003224333668971\n", "                ],\n", "                [\n", "                    39,\n", "                    -0.332484984763191\n", "                ],\n", "                [\n", "                    40,\n", "                    -0.3042649326119893\n", "                ],\n", "                [\n", "                    41,\n", "                    -0.32495560477145347\n", "                ],\n", "                [\n", "                    42,\n", "                    -0.3127337028477122\n", "                ],\n", "                [\n", "                    43,\n", "                    -0.30517826740492104\n", "                ],\n", "                [\n", "                    44,\n", "                    -0.2965786724864383\n", "                ],\n", "                [\n", "                    45,\n", "                    -0.29922481695216874\n", "                ],\n", "                [\n", "                    46,\n", "                    -0.3593125390611931\n", "                ],\n", "                [\n", "                    47,\n", "                    -0.4294175915504699\n", "                ],\n", "                [\n", "                    48,\n", "                    -0.5352900105486178\n", "                ],\n", "                [\n", "                    49,\n", "                    -0.650428914201882\n", "                ],\n", "                [\n", "                    50,\n", "                    -0.7100912584977248\n", "                ],\n", "                [\n", "                    51,\n", "                    -0.7782587218805315\n", "                ],\n", "                [\n", "                    52,\n", "                    -0.7861020767031803\n", "                ],\n", "                [\n", "                    53,\n", "                    -0.762547986302657\n", "                ],\n", "                [\n", "                    54,\n", "                    -0.7202472261734414\n", "                ],\n", "                [\n", "                    55,\n", "                    -0.6717181722126782\n", "                ],\n", "                [\n", "                    56,\n", "                    -0.6427940624461232\n", "                ],\n", "                [\n", "                    57,\n", "                    -0.6239755255925985\n", "                ],\n", "                [\n", "                    58,\n", "                    -0.5949413383074429\n", "                ],\n", "                [\n", "                    59,\n", "                    -0.5590320310235448\n", "                ],\n", "                [\n", "                    60,\n", "                    -0.5532451716868909\n", "                ],\n", "                [\n", "                    61,\n", "                    -0.5208680577144467\n", "                ],\n", "                [\n", "                    62,\n", "                    -0.4975427626338771\n", "                ],\n", "                [\n", "                    63,\n", "                    -0.4767888389937287\n", "                ],\n", "                [\n", "                    64,\n", "                    -0.45110654737830025\n", "                ],\n", "                [\n", "                    65,\n", "                    -0.44179869443312647\n", "                ],\n", "                [\n", "                    66,\n", "                    -0.40793301255946446\n", "                ],\n", "                [\n", "                    67,\n", "                    -0.37595354086289845\n", "                ],\n", "                [\n", "                    68,\n", "                    -0.3617707235409391\n", "                ],\n", "                [\n", "                    69,\n", "                    -0.32978394958736956\n", "                ],\n", "                [\n", "                    70,\n", "                    -0.29298765717830655\n", "                ],\n", "                [\n", "                    71,\n", "                    -0.2241246057788615\n", "                ],\n", "                [\n", "                    72,\n", "                    -0.1851678036194233\n", "                ],\n", "                [\n", "                    73,\n", "                    -0.1525359181256256\n", "                ],\n", "                [\n", "                    74,\n", "                    -0.1244336042130989\n", "                ],\n", "                [\n", "                    75,\n", "                    -0.08025733429059834\n", "                ],\n", "                [\n", "                    76,\n", "                    -0.04313624151039974\n", "                ],\n", "                [\n", "                    77,\n", "                    0.026324856724917467\n", "                ],\n", "                [\n", "                    78,\n", "                    0.07326645953919808\n", "                ],\n", "                [\n", "                    79,\n", "                    0.09644559041034029\n", "                ],\n", "                [\n", "                    80,\n", "                    0.10712501670071717\n", "                ],\n", "                [\n", "                    81,\n", "                    0.1350120314102643\n", "                ],\n", "                [\n", "                    82,\n", "                    0.1593108374486807\n", "                ],\n", "                [\n", "                    83,\n", "                    0.159780718675945\n", "                ],\n", "                [\n", "                    84,\n", "                    0.14556446358418995\n", "                ],\n", "                [\n", "                    85,\n", "                    0.10644273137743987\n", "                ],\n", "                [\n", "                    86,\n", "                    0.06899473342198092\n", "                ],\n", "                [\n", "                    87,\n", "                    0.03647570672272238\n", "                ],\n", "                [\n", "                    88,\n", "                    0.02334569199569536\n", "                ],\n", "                [\n", "                    89,\n", "                    0.024758407614170252\n", "                ],\n", "                [\n", "                    90,\n", "                    0.01680816021993614\n", "                ],\n", "                [\n", "                    91,\n", "                    -0.02790281036209663\n", "                ],\n", "                [\n", "                    92,\n", "                    -0.059423909133068165\n", "                ],\n", "                [\n", "                    93,\n", "                    -0.07706096476123037\n", "                ],\n", "                [\n", "                    94,\n", "                    -0.09319186629598697\n", "                ],\n", "                [\n", "                    95,\n", "                    -0.09918397585973082\n", "                ],\n", "                [\n", "                    96,\n", "                    -0.10833239245970461\n", "                ],\n", "                [\n", "                    97,\n", "                    -0.08873833261683828\n", "                ],\n", "                [\n", "                    98,\n", "                    -0.054028019832633944\n", "                ],\n", "                [\n", "                    99,\n", "                    -0.027015347197345108\n", "                ],\n", "                [\n", "                    100,\n", "                    -0.0031505427429472377\n", "                ],\n", "                [\n", "                    101,\n", "                    0.017178298840903494\n", "                ],\n", "                [\n", "                    102,\n", "                    0.035302852231772874\n", "                ],\n", "                [\n", "                    103,\n", "                    0.05308929318209188\n", "                ],\n", "                [\n", "                    104,\n", "                    0.05206055839180479\n", "                ],\n", "                [\n", "                    105,\n", "                    0.017954734172727527\n", "                ],\n", "                [\n", "                    106,\n", "                    -0.0161504527131342\n", "                ],\n", "                [\n", "                    107,\n", "                    -0.05624824354720559\n", "                ],\n", "                [\n", "                    108,\n", "                    -0.08143884042806171\n", "                ],\n", "                [\n", "                    109,\n", "                    -0.10343786829280077\n", "                ],\n", "                [\n", "                    110,\n", "                    -0.12667428193847563\n", "                ],\n", "                [\n", "                    111,\n", "                    -0.14024498457325407\n", "                ],\n", "                [\n", "                    112,\n", "                    -0.16204260077345545\n", "                ],\n", "                [\n", "                    113,\n", "                    -0.1804647490696425\n", "                ],\n", "                [\n", "                    114,\n", "                    -0.17688706184791236\n", "                ],\n", "                [\n", "                    115,\n", "                    -0.1720682235762112\n", "                ],\n", "                [\n", "                    116,\n", "                    -0.16633188904908103\n", "                ],\n", "                [\n", "                    117,\n", "                    -0.13441502423096985\n", "                ],\n", "                [\n", "                    118,\n", "                    -0.0831477936842333\n", "                ],\n", "                [\n", "                    119,\n", "                    0.023379498552488442\n", "                ],\n", "                [\n", "                    120,\n", "                    0.22064869334134407\n", "                ],\n", "                [\n", "                    121,\n", "                    0.3567353993059328\n", "                ],\n", "                [\n", "                    122,\n", "                    0.48162685954250684\n", "                ],\n", "                [\n", "                    123,\n", "                    0.5556400906732435\n", "                ],\n", "                [\n", "                    124,\n", "                    0.5538482906682827\n", "                ],\n", "                [\n", "                    125,\n", "                    0.5485259597166099\n", "                ],\n", "                [\n", "                    126,\n", "                    0.5213528974642561\n", "                ],\n", "                [\n", "                    127,\n", "                    0.4614155649592586\n", "                ],\n", "                [\n", "                    128,\n", "                    0.3996251698062352\n", "                ],\n", "                [\n", "                    129,\n", "                    0.34586203776547464\n", "                ],\n", "                [\n", "                    130,\n", "                    0.34686400510943116\n", "                ],\n", "                [\n", "                    131,\n", "                    0.3245508569588633\n", "                ],\n", "                [\n", "                    132,\n", "                    0.2969886940835309\n", "                ],\n", "                [\n", "                    133,\n", "                    0.24010108847939726\n", "                ],\n", "                [\n", "                    134,\n", "                    0.15211107168437188\n", "                ],\n", "                [\n", "                    135,\n", "                    0.06069882409072669\n", "                ],\n", "                [\n", "                    136,\n", "                    -0.003634974725841289\n", "                ],\n", "                [\n", "                    137,\n", "                    -0.03804310798981092\n", "                ],\n", "                [\n", "                    138,\n", "                    -0.07812876102580013\n", "                ],\n", "                [\n", "                    139,\n", "                    -0.11103770558646531\n", "                ],\n", "                [\n", "                    140,\n", "                    -0.11561265980822277\n", "                ],\n", "                [\n", "                    141,\n", "                    -0.08198206656534701\n", "                ],\n", "                [\n", "                    142,\n", "                    -0.07703522494696458\n", "                ],\n", "                [\n", "                    143,\n", "                    -0.061113508864790944\n", "                ],\n", "                [\n", "                    144,\n", "                    -0.06389719559397378\n", "                ],\n", "                [\n", "                    145,\n", "                    -0.045406953622888935\n", "                ],\n", "                [\n", "                    146,\n", "                    -0.01604386428801874\n", "                ],\n", "                [\n", "                    147,\n", "                    0.02708726113717219\n", "                ],\n", "                [\n", "                    148,\n", "                    0.044616350730843024\n", "                ],\n", "                [\n", "                    149,\n", "                    0.08097542262638768\n", "                ],\n", "                [\n", "                    150,\n", "                    0.16597497556305285\n", "                ],\n", "                [\n", "                    151,\n", "                    0.22748773099134567\n", "                ],\n", "                [\n", "                    152,\n", "                    0.26910042848084714\n", "                ],\n", "                [\n", "                    153,\n", "                    0.25795253701557463\n", "                ],\n", "                [\n", "                    154,\n", "                    0.23511071279616758\n", "                ],\n", "                [\n", "                    155,\n", "                    0.21533309310522242\n", "                ],\n", "                [\n", "                    156,\n", "                    0.20855195980769814\n", "                ],\n", "                [\n", "                    157,\n", "                    0.18251486722635946\n", "                ],\n", "                [\n", "                    158,\n", "                    0.16721498138939772\n", "                ],\n", "                [\n", "                    159,\n", "                    0.20676960095999064\n", "                ],\n", "                [\n", "                    160,\n", "                    0.231414734800655\n", "                ],\n", "                [\n", "                    161,\n", "                    0.25287272017666673\n", "                ],\n", "                [\n", "                    162,\n", "                    0.2811617660390482\n", "                ],\n", "                [\n", "                    163,\n", "                    0.2666171522156837\n", "                ],\n", "                [\n", "                    164,\n", "                    0.2569697521863734\n", "                ],\n", "                [\n", "                    165,\n", "                    0.2448873760701158\n", "                ],\n", "                [\n", "                    166,\n", "                    0.2717187067360687\n", "                ],\n", "                [\n", "                    167,\n", "                    0.27209403752294214\n", "                ],\n", "                [\n", "                    168,\n", "                    0.2796576837232223\n", "                ],\n", "                [\n", "                    169,\n", "                    0.2560718488804419\n", "                ],\n", "                [\n", "                    170,\n", "                    0.25780862387392744\n", "                ],\n", "                [\n", "                    171,\n", "                    0.26021995899561645\n", "                ],\n", "                [\n", "                    172,\n", "                    0.26632319923060876\n", "                ],\n", "                [\n", "                    173,\n", "                    0.27844028831237466\n", "                ],\n", "                [\n", "                    174,\n", "                    0.3246466653614828\n", "                ],\n", "                [\n", "                    175,\n", "                    0.34039642897133504\n", "                ],\n", "                [\n", "                    176,\n", "                    0.32572292263750136\n", "                ],\n", "                [\n", "                    177,\n", "                    0.31530096841427024\n", "                ],\n", "                [\n", "                    178,\n", "                    0.2628586855508903\n", "                ],\n", "                [\n", "                    179,\n", "                    0.22435993190521408\n", "                ],\n", "                [\n", "                    180,\n", "                    0.201212931961674\n", "                ],\n", "                [\n", "                    181,\n", "                    0.14010103249637673\n", "                ],\n", "                [\n", "                    182,\n", "                    0.12014041573585743\n", "                ],\n", "                [\n", "                    183,\n", "                    0.10393035513162374\n", "                ],\n", "                [\n", "                    184,\n", "                    0.1474816711239555\n", "                ],\n", "                [\n", "                    185,\n", "                    0.19268590250115203\n", "                ],\n", "                [\n", "                    186,\n", "                    0.22351330996190555\n", "                ],\n", "                [\n", "                    187,\n", "                    0.28739787922646265\n", "                ],\n", "                [\n", "                    188,\n", "                    0.3772516257530931\n", "                ],\n", "                [\n", "                    189,\n", "                    0.4736640866984221\n", "                ],\n", "                [\n", "                    190,\n", "                    0.5485893585308226\n", "                ],\n", "                [\n", "                    191,\n", "                    0.6305554335159673\n", "                ],\n", "                [\n", "                    192,\n", "                    0.6596678235069966\n", "                ],\n", "                [\n", "                    193,\n", "                    0.7204291794998916\n", "                ],\n", "                [\n", "                    194,\n", "                    0.7255222170498783\n", "                ],\n", "                [\n", "                    195,\n", "                    0.7260307539673931\n", "                ],\n", "                [\n", "                    196,\n", "                    0.7077849552108297\n", "                ],\n", "                [\n", "                    197,\n", "                    0.6965919794874758\n", "                ],\n", "                [\n", "                    198,\n", "                    0.6782887423311443\n", "                ],\n", "                [\n", "                    199,\n", "                    0.6466461634499048\n", "                ],\n", "                [\n", "                    200,\n", "                    0.6408105684466499\n", "                ],\n", "                [\n", "                    201,\n", "                    0.6576537852566737\n", "                ],\n", "                [\n", "                    202,\n", "                    0.6138967182530592\n", "                ],\n", "                [\n", "                    203,\n", "                    0.5678318171162857\n", "                ],\n", "                [\n", "                    204,\n", "                    0.5412244807029616\n", "                ],\n", "                [\n", "                    205,\n", "                    0.5357489381488953\n", "                ],\n", "                [\n", "                    206,\n", "                    0.5014219582202273\n", "                ],\n", "                [\n", "                    207,\n", "                    0.45684757841035406\n", "                ],\n", "                [\n", "                    208,\n", "                    0.37842779276546423\n", "                ],\n", "                [\n", "                    209,\n", "                    0.32783191748510276\n", "                ],\n", "                [\n", "                    210,\n", "                    0.32115045488669836\n", "                ],\n", "                [\n", "                    211,\n", "                    0.36251226675533843\n", "                ],\n", "                [\n", "                    212,\n", "                    0.4218981619030693\n", "                ],\n", "                [\n", "                    213,\n", "                    0.4644153371680666\n", "                ],\n", "                [\n", "                    214,\n", "                    0.5530608125219203\n", "                ],\n", "                [\n", "                    215,\n", "                    0.5954689677134368\n", "                ],\n", "                [\n", "                    216,\n", "                    0.5979771352542151\n", "                ],\n", "                [\n", "                    217,\n", "                    0.6282273846351174\n", "                ],\n", "                [\n", "                    218,\n", "                    0.6607228519889858\n", "                ],\n", "                [\n", "                    219,\n", "                    0.6818435417734818\n", "                ],\n", "                [\n", "                    220,\n", "                    0.7153501387069383\n", "                ],\n", "                [\n", "                    221,\n", "                    0.6999453341538668\n", "                ],\n", "                [\n", "                    222,\n", "                    0.6727199706532687\n", "                ],\n", "                [\n", "                    223,\n", "                    0.6245779340570365\n", "                ],\n", "                [\n", "                    224,\n", "                    0.5486310058513588\n", "                ],\n", "                [\n", "                    225,\n", "                    0.46692180562353514\n", "                ],\n", "                [\n", "                    226,\n", "                    0.39758357973992986\n", "                ],\n", "                [\n", "                    227,\n", "                    0.33394156709904266\n", "                ],\n", "                [\n", "                    228,\n", "                    0.2699036191086144\n", "                ],\n", "                [\n", "                    229,\n", "                    0.24537356163339652\n", "                ],\n", "                [\n", "                    230,\n", "                    0.2121904845615461\n", "                ],\n", "                [\n", "                    231,\n", "                    0.20531268361281718\n", "                ],\n", "                [\n", "                    232,\n", "                    0.19279802265739931\n", "                ],\n", "                [\n", "                    233,\n", "                    0.13373043333156076\n", "                ],\n", "                [\n", "                    234,\n", "                    0.07954664758246466\n", "                ],\n", "                [\n", "                    235,\n", "                    0.0058750587752527395\n", "                ],\n", "                [\n", "                    236,\n", "                    -0.04393459124493049\n", "                ],\n", "                [\n", "                    237,\n", "                    -0.06171782597590081\n", "                ],\n", "                [\n", "                    238,\n", "                    -0.09249708024118064\n", "                ],\n", "                [\n", "                    239,\n", "                    -0.050942398195559235\n", "                ],\n", "                [\n", "                    240,\n", "                    0.007722294554660891\n", "                ],\n", "                [\n", "                    241,\n", "                    0.05598981970592476\n", "                ],\n", "                [\n", "                    242,\n", "                    0.10433628733490963\n", "                ],\n", "                [\n", "                    243,\n", "                    0.08199421738798662\n", "                ],\n", "                [\n", "                    244,\n", "                    0.0292533452940944\n", "                ],\n", "                [\n", "                    245,\n", "                    0.09848196863598702\n", "                ],\n", "                [\n", "                    246,\n", "                    0.1787211240333555\n", "                ],\n", "                [\n", "                    247,\n", "                    0.23556124721894633\n", "                ],\n", "                [\n", "                    248,\n", "                    0.3196888508860738\n", "                ],\n", "                [\n", "                    249,\n", "                    0.43141630275193066\n", "                ],\n", "                [\n", "                    250,\n", "                    0.4901040066040778\n", "                ],\n", "                [\n", "                    251,\n", "                    0.48821996823633995\n", "                ],\n", "                [\n", "                    252,\n", "                    0.5473909356891227\n", "                ],\n", "                [\n", "                    253,\n", "                    0.7231244180445593\n", "                ],\n", "                [\n", "                    254,\n", "                    0.8238486629891959\n", "                ],\n", "                [\n", "                    255,\n", "                    0.9037454513305576\n", "                ],\n", "                [\n", "                    256,\n", "                    0.9368982575657476\n", "                ],\n", "                [\n", "                    257,\n", "                    0.9362413592307455\n", "                ],\n", "                [\n", "                    258,\n", "                    0.9617524347890125\n", "                ],\n", "                [\n", "                    259,\n", "                    0.961206953419687\n", "                ],\n", "                [\n", "                    260,\n", "                    1.0064638332195663\n", "                ],\n", "                [\n", "                    261,\n", "                    1.0089133677269402\n", "                ],\n", "                [\n", "                    262,\n", "                    1.021671112794234\n", "                ],\n", "                [\n", "                    263,\n", "                    1.1364907579290744\n", "                ],\n", "                [\n", "                    264,\n", "                    1.1121871664201421\n", "                ],\n", "                [\n", "                    265,\n", "                    1.2136907666177592\n", "                ],\n", "                [\n", "                    266,\n", "                    1.2514649175803605\n", "                ],\n", "                [\n", "                    267,\n", "                    1.2931231279632485\n", "                ],\n", "                [\n", "                    268,\n", "                    1.300654510708096\n", "                ],\n", "                [\n", "                    269,\n", "                    1.2741830355286083\n", "                ],\n", "                [\n", "                    270,\n", "                    1.1782958907523344\n", "                ],\n", "                [\n", "                    271,\n", "                    1.1280333597651975\n", "                ],\n", "                [\n", "                    272,\n", "                    1.0399013670001729\n", "                ],\n", "                [\n", "                    273,\n", "                    0.8417363998252938\n", "                ],\n", "                [\n", "                    274,\n", "                    0.6409890734082211\n", "                ],\n", "                [\n", "                    275,\n", "                    0.4947512641252345\n", "                ],\n", "                [\n", "                    276,\n", "                    0.3689552452952576\n", "                ],\n", "                [\n", "                    277,\n", "                    0.20955431606612862\n", "                ],\n", "                [\n", "                    278,\n", "                    0.08786351036163964\n", "                ],\n", "                [\n", "                    279,\n", "                    0.00747483340955668\n", "                ],\n", "                [\n", "                    280,\n", "                    0.052897096955252465\n", "                ],\n", "                [\n", "                    281,\n", "                    0.08070203737475623\n", "                ],\n", "                [\n", "                    282,\n", "                    0.05529904724901158\n", "                ],\n", "                [\n", "                    283,\n", "                    -0.024265107946359166\n", "                ],\n", "                [\n", "                    284,\n", "                    -0.14615423115701986\n", "                ],\n", "                [\n", "                    285,\n", "                    -0.27588326807526187\n", "                ],\n", "                [\n", "                    286,\n", "                    -0.3049771486456798\n", "                ],\n", "                [\n", "                    287,\n", "                    -0.3075438397063124\n", "                ],\n", "                [\n", "                    288,\n", "                    -0.29647735732126534\n", "                ],\n", "                [\n", "                    289,\n", "                    -0.27964205223530314\n", "                ],\n", "                [\n", "                    290,\n", "                    -0.2999603510741018\n", "                ],\n", "                [\n", "                    291,\n", "                    -0.2701816923173297\n", "                ],\n", "                [\n", "                    292,\n", "                    -0.34428466118519196\n", "                ],\n", "                [\n", "                    293,\n", "                    -0.312265224700635\n", "                ],\n", "                [\n", "                    294,\n", "                    -0.3091472590822413\n", "                ],\n", "                [\n", "                    295,\n", "                    -0.3590218186794303\n", "                ],\n", "                [\n", "                    296,\n", "                    -0.37645605404840055\n", "                ],\n", "                [\n", "                    297,\n", "                    -0.35471414667026124\n", "                ],\n", "                [\n", "                    298,\n", "                    -0.3057173343749078\n", "                ],\n", "                [\n", "                    299,\n", "                    -0.22874584514778107\n", "                ],\n", "                [\n", "                    300,\n", "                    -0.15945199068039884\n", "                ],\n", "                [\n", "                    301,\n", "                    -0.12169239540617838\n", "                ],\n", "                [\n", "                    302,\n", "                    -0.1130580413263047\n", "                ],\n", "                [\n", "                    303,\n", "                    -0.09064584891242689\n", "                ],\n", "                [\n", "                    304,\n", "                    -0.0752443209652327\n", "                ],\n", "                [\n", "                    305,\n", "                    -0.06870187803264116\n", "                ],\n", "                [\n", "                    306,\n", "                    -0.08353384578593648\n", "                ],\n", "                [\n", "                    307,\n", "                    -0.14206562854665705\n", "                ],\n", "                [\n", "                    308,\n", "                    -0.1799231149996814\n", "                ],\n", "                [\n", "                    309,\n", "                    -0.21630806176487383\n", "                ],\n", "                [\n", "                    310,\n", "                    -0.2670790820308824\n", "                ],\n", "                [\n", "                    311,\n", "                    -0.3117905186264025\n", "                ],\n", "                [\n", "                    312,\n", "                    -0.2722705271428225\n", "                ],\n", "                [\n", "                    313,\n", "                    -0.19512784974522646\n", "                ],\n", "                [\n", "                    314,\n", "                    -0.027165605633204137\n", "                ],\n", "                [\n", "                    315,\n", "                    0.10234516232502244\n", "                ],\n", "                [\n", "                    316,\n", "                    0.22737673081281073\n", "                ],\n", "                [\n", "                    317,\n", "                    0.2948245211572278\n", "                ],\n", "                [\n", "                    318,\n", "                    0.34430840793809736\n", "                ],\n", "                [\n", "                    319,\n", "                    0.4118606450691118\n", "                ],\n", "                [\n", "                    320,\n", "                    0.47923791752139877\n", "                ],\n", "                [\n", "                    321,\n", "                    0.5026333541433203\n", "                ],\n", "                [\n", "                    322,\n", "                    0.5319872335007432\n", "                ],\n", "                [\n", "                    323,\n", "                    0.5927973984274892\n", "                ],\n", "                [\n", "                    324,\n", "                    0.6185284689720625\n", "                ],\n", "                [\n", "                    325,\n", "                    0.605314595820694\n", "                ],\n", "                [\n", "                    326,\n", "                    0.5896591157639719\n", "                ],\n", "                [\n", "                    327,\n", "                    0.5323830659672346\n", "                ],\n", "                [\n", "                    328,\n", "                    0.5013846444345553\n", "                ],\n", "                [\n", "                    329,\n", "                    0.4937205290521476\n", "                ],\n", "                [\n", "                    330,\n", "                    0.5060210594043646\n", "                ],\n", "                [\n", "                    331,\n", "                    0.4859599813680191\n", "                ],\n", "                [\n", "                    332,\n", "                    0.48225446460030597\n", "                ],\n", "                [\n", "                    333,\n", "                    0.44753071824839097\n", "                ],\n", "                [\n", "                    334,\n", "                    0.4144277179071665\n", "                ],\n", "                [\n", "                    335,\n", "                    0.4731142251280609\n", "                ],\n", "                [\n", "                    336,\n", "                    0.5464086199577878\n", "                ],\n", "                [\n", "                    337,\n", "                    0.5800562669616163\n", "                ],\n", "                [\n", "                    338,\n", "                    0.5766741525975299\n", "                ],\n", "                [\n", "                    339,\n", "                    0.5435209387499107\n", "                ],\n", "                [\n", "                    340,\n", "                    0.48901604540776233\n", "                ],\n", "                [\n", "                    341,\n", "                    0.43834678657379555\n", "                ],\n", "                [\n", "                    342,\n", "                    0.3840805947628674\n", "                ],\n", "                [\n", "                    343,\n", "                    0.39861186220715084\n", "                ],\n", "                [\n", "                    344,\n", "                    0.38630887388336177\n", "                ],\n", "                [\n", "                    345,\n", "                    0.39061497848571136\n", "                ],\n", "                [\n", "                    346,\n", "                    0.39910990398913526\n", "                ],\n", "                [\n", "                    347,\n", "                    0.36531977549194394\n", "                ],\n", "                [\n", "                    348,\n", "                    0.2684720135583376\n", "                ],\n", "                [\n", "                    349,\n", "                    0.17756881530769775\n", "                ],\n", "                [\n", "                    350,\n", "                    0.10751566778588284\n", "                ],\n", "                [\n", "                    351,\n", "                    0.0386419406074161\n", "                ],\n", "                [\n", "                    352,\n", "                    -0.051656756647595614\n", "                ],\n", "                [\n", "                    353,\n", "                    -0.16090323732719014\n", "                ],\n", "                [\n", "                    354,\n", "                    -0.18004616962798892\n", "                ],\n", "                [\n", "                    355,\n", "                    -0.17943113174823822\n", "                ],\n", "                [\n", "                    356,\n", "                    -0.18169079002765542\n", "                ],\n", "                [\n", "                    357,\n", "                    -0.15905444491862042\n", "                ],\n", "                [\n", "                    358,\n", "                    -0.18577463670021999\n", "                ],\n", "                [\n", "                    359,\n", "                    -0.23969188511372153\n", "                ],\n", "                [\n", "                    360,\n", "                    -0.2568670297668767\n", "                ],\n", "                [\n", "                    361,\n", "                    -0.22112827381402766\n", "                ],\n", "                [\n", "                    362,\n", "                    -0.30149103454824555\n", "                ],\n", "                [\n", "                    363,\n", "                    -0.34107449626823794\n", "                ],\n", "                [\n", "                    364,\n", "                    -0.3107643936697322\n", "                ],\n", "                [\n", "                    365,\n", "                    -0.30182326606497867\n", "                ],\n", "                [\n", "                    366,\n", "                    -0.3743414906929914\n", "                ],\n", "                [\n", "                    367,\n", "                    -0.4460370628010608\n", "                ],\n", "                [\n", "                    368,\n", "                    -0.5059006926543823\n", "                ],\n", "                [\n", "                    369,\n", "                    -0.5502280300875597\n", "                ],\n", "                [\n", "                    370,\n", "                    -0.6074049693290782\n", "                ],\n", "                [\n", "                    371,\n", "                    -0.5766757340416184\n", "                ],\n", "                [\n", "                    372,\n", "                    -0.5683645014484036\n", "                ],\n", "                [\n", "                    373,\n", "                    -0.5657461340155621\n", "                ],\n", "                [\n", "                    374,\n", "                    -0.605908425810167\n", "                ],\n", "                [\n", "                    375,\n", "                    -0.6424354814804687\n", "                ],\n", "                [\n", "                    376,\n", "                    -0.6693163885557141\n", "                ],\n", "                [\n", "                    377,\n", "                    -0.7050855734316954\n", "                ],\n", "                [\n", "                    378,\n", "                    -0.8287784040504071\n", "                ],\n", "                [\n", "                    379,\n", "                    -0.9992069362553764\n", "                ],\n", "                [\n", "                    380,\n", "                    -1.122144311216477\n", "                ],\n", "                [\n", "                    381,\n", "                    -1.1889226612718495\n", "                ],\n", "                [\n", "                    382,\n", "                    -1.249231366742336\n", "                ],\n", "                [\n", "                    383,\n", "                    -1.2567184565925196\n", "                ],\n", "                [\n", "                    384,\n", "                    -1.2578354818985886\n", "                ],\n", "                [\n", "                    385,\n", "                    -1.250758103071167\n", "                ],\n", "                [\n", "                    386,\n", "                    -1.2413298649235145\n", "                ],\n", "                [\n", "                    387,\n", "                    -1.2054378850081342\n", "                ],\n", "                [\n", "                    388,\n", "                    -1.0678537083222004\n", "                ],\n", "                [\n", "                    389,\n", "                    -0.8944432712227126\n", "                ],\n", "                [\n", "                    390,\n", "                    -0.7420055410596085\n", "                ],\n", "                [\n", "                    391,\n", "                    -0.6077366010052145\n", "                ],\n", "                [\n", "                    392,\n", "                    -0.4956143647688087\n", "                ],\n", "                [\n", "                    393,\n", "                    -0.39733495615317693\n", "                ],\n", "                [\n", "                    394,\n", "                    -0.33814356813633495\n", "                ],\n", "                [\n", "                    395,\n", "                    -0.21213158529537068\n", "                ],\n", "                [\n", "                    396,\n", "                    -0.13332297668441484\n", "                ],\n", "                [\n", "                    397,\n", "                    -0.1434493035369755\n", "                ],\n", "                [\n", "                    398,\n", "                    -0.1593209350763125\n", "                ],\n", "                [\n", "                    399,\n", "                    -0.1651540260535569\n", "                ],\n", "                [\n", "                    400,\n", "                    -0.1837964303684494\n", "                ],\n", "                [\n", "                    401,\n", "                    -0.25214821232036044\n", "                ],\n", "                [\n", "                    402,\n", "                    -0.30841073992005974\n", "                ],\n", "                [\n", "                    403,\n", "                    -0.4024237089520746\n", "                ],\n", "                [\n", "                    404,\n", "                    -0.4651128019663098\n", "                ],\n", "                [\n", "                    405,\n", "                    -0.502545991057616\n", "                ],\n", "                [\n", "                    406,\n", "                    -0.4846654868863318\n", "                ],\n", "                [\n", "                    407,\n", "                    -0.4938512401779285\n", "                ],\n", "                [\n", "                    408,\n", "                    -0.46271356836732735\n", "                ],\n", "                [\n", "                    409,\n", "                    -0.37002492443175683\n", "                ],\n", "                [\n", "                    410,\n", "                    -0.293986529047114\n", "                ],\n", "                [\n", "                    411,\n", "                    -0.2494095921611752\n", "                ],\n", "                [\n", "                    412,\n", "                    -0.08640020181280406\n", "                ],\n", "                [\n", "                    413,\n", "                    0.013580376970821817\n", "                ],\n", "                [\n", "                    414,\n", "                    0.024749430126419725\n", "                ],\n", "                [\n", "                    415,\n", "                    0.045183879976228525\n", "                ],\n", "                [\n", "                    416,\n", "                    0.025579145471365194\n", "                ],\n", "                [\n", "                    417,\n", "                    -0.033946833362605844\n", "                ],\n", "                [\n", "                    418,\n", "                    -0.12407178164149713\n", "                ],\n", "                [\n", "                    419,\n", "                    -0.21720019659912992\n", "                ],\n", "                [\n", "                    420,\n", "                    -0.31401362488104567\n", "                ],\n", "                [\n", "                    421,\n", "                    -0.36873624105768954\n", "                ],\n", "                [\n", "                    422,\n", "                    -0.37549913132648527\n", "                ],\n", "                [\n", "                    423,\n", "                    -0.3621595253396279\n", "                ],\n", "                [\n", "                    424,\n", "                    -0.36513095055972755\n", "                ],\n", "                [\n", "                    425,\n", "                    -0.30665977715029413\n", "                ],\n", "                [\n", "                    426,\n", "                    -0.1967275498107064\n", "                ],\n", "                [\n", "                    427,\n", "                    -0.11234500645563728\n", "                ],\n", "                [\n", "                    428,\n", "                    -0.026605463867412027\n", "                ],\n", "                [\n", "                    429,\n", "                    0.011356918090509538\n", "                ],\n", "                [\n", "                    430,\n", "                    0.07686753736705398\n", "                ],\n", "                [\n", "                    431,\n", "                    0.09780186828324133\n", "                ],\n", "                [\n", "                    432,\n", "                    0.13542504073001993\n", "                ],\n", "                [\n", "                    433,\n", "                    0.13703379360735113\n", "                ],\n", "                [\n", "                    434,\n", "                    0.1973593544815273\n", "                ],\n", "                [\n", "                    435,\n", "                    0.245564744289144\n", "                ],\n", "                [\n", "                    436,\n", "                    0.28691580759381097\n", "                ],\n", "                [\n", "                    437,\n", "                    0.31045962291691254\n", "                ],\n", "                [\n", "                    438,\n", "                    0.31499726030205366\n", "                ],\n", "                [\n", "                    439,\n", "                    0.2990082609849729\n", "                ],\n", "                [\n", "                    440,\n", "                    0.26552390811848525\n", "                ],\n", "                [\n", "                    441,\n", "                    0.2274888862644744\n", "                ],\n", "                [\n", "                    442,\n", "                    0.09857268525333396\n", "                ],\n", "                [\n", "                    443,\n", "                    -0.015519183746885545\n", "                ],\n", "                [\n", "                    444,\n", "                    -0.11749407616874663\n", "                ],\n", "                [\n", "                    445,\n", "                    -0.2143975494204149\n", "                ],\n", "                [\n", "                    446,\n", "                    -0.30542565468118\n", "                ],\n", "                [\n", "                    447,\n", "                    -0.36448840775236135\n", "                ],\n", "                [\n", "                    448,\n", "                    -0.41697930081095436\n", "                ],\n", "                [\n", "                    449,\n", "                    -0.37756926772796007\n", "                ],\n", "                [\n", "                    450,\n", "                    -0.34877144388541126\n", "                ],\n", "                [\n", "                    451,\n", "                    -0.30947091017531747\n", "                ],\n", "                [\n", "                    452,\n", "                    -0.2919052964688298\n", "                ],\n", "                [\n", "                    453,\n", "                    -0.28359143619771743\n", "                ],\n", "                [\n", "                    454,\n", "                    -0.29857526149872626\n", "                ],\n", "                [\n", "                    455,\n", "                    -0.27899193076306616\n", "                ],\n", "                [\n", "                    456,\n", "                    -0.26286263254267084\n", "                ],\n", "                [\n", "                    457,\n", "                    -0.2663754315717881\n", "                ],\n", "                [\n", "                    458,\n", "                    -0.2668897278872322\n", "                ],\n", "                [\n", "                    459,\n", "                    -0.27940788337212297\n", "                ],\n", "                [\n", "                    460,\n", "                    -0.29400862760248714\n", "                ],\n", "                [\n", "                    461,\n", "                    -0.3076814770410614\n", "                ],\n", "                [\n", "                    462,\n", "                    -0.32047152743264107\n", "                ],\n", "                [\n", "                    463,\n", "                    -0.3108857090507442\n", "                ],\n", "                [\n", "                    464,\n", "                    -0.30382119938913377\n", "                ],\n", "                [\n", "                    465,\n", "                    -0.2900376525102182\n", "                ],\n", "                [\n", "                    466,\n", "                    -0.2400358690294695\n", "                ],\n", "                [\n", "                    467,\n", "                    -0.16940728232484403\n", "                ],\n", "                [\n", "                    468,\n", "                    -0.11134320690298694\n", "                ],\n", "                [\n", "                    469,\n", "                    -0.04623496311773678\n", "                ],\n", "                [\n", "                    470,\n", "                    -0.020224402167837496\n", "                ],\n", "                [\n", "                    471,\n", "                    -0.008390208806318356\n", "                ],\n", "                [\n", "                    472,\n", "                    -0.05406552501816009\n", "                ],\n", "                [\n", "                    473,\n", "                    -0.09162806022666103\n", "                ],\n", "                [\n", "                    474,\n", "                    -0.10645194894868482\n", "                ],\n", "                [\n", "                    475,\n", "                    -0.12881879112179817\n", "                ],\n", "                [\n", "                    476,\n", "                    -0.14886325389421629\n", "                ],\n", "                [\n", "                    477,\n", "                    -0.1572871121473156\n", "                ],\n", "                [\n", "                    478,\n", "                    -0.17804897132399233\n", "                ],\n", "                [\n", "                    479,\n", "                    -0.19787038268664148\n", "                ],\n", "                [\n", "                    480,\n", "                    -0.21194276614528107\n", "                ],\n", "                [\n", "                    481,\n", "                    -0.2277323228021011\n", "                ],\n", "                [\n", "                    482,\n", "                    -0.24149642191725107\n", "                ],\n", "                [\n", "                    483,\n", "                    -0.28303243778334064\n", "                ],\n", "                [\n", "                    484,\n", "                    -0.30676541388602274\n", "                ],\n", "                [\n", "                    485,\n", "                    -0.3489862309022449\n", "                ],\n", "                [\n", "                    486,\n", "                    -0.3637291931696254\n", "                ],\n", "                [\n", "                    487,\n", "                    -0.33204656596407744\n", "                ],\n", "                [\n", "                    488,\n", "                    -0.3058331507350651\n", "                ],\n", "                [\n", "                    489,\n", "                    -0.27542853024812075\n", "                ],\n", "                [\n", "                    490,\n", "                    -0.24926618051905436\n", "                ],\n", "                [\n", "                    491,\n", "                    -0.20837811528381422\n", "                ],\n", "                [\n", "                    492,\n", "                    -0.2066751796689239\n", "                ],\n", "                [\n", "                    493,\n", "                    -0.2045811397465016\n", "                ],\n", "                [\n", "                    494,\n", "                    -0.25246095302954075\n", "                ],\n", "                [\n", "                    495,\n", "                    -0.29587150813130236\n", "                ],\n", "                [\n", "                    496,\n", "                    -0.30257924779073164\n", "                ],\n", "                [\n", "                    497,\n", "                    -0.3059818444151752\n", "                ],\n", "                [\n", "                    498,\n", "                    -0.23894989767460828\n", "                ],\n", "                [\n", "                    499,\n", "                    -0.18211347333172156\n", "                ],\n", "                [\n", "                    500,\n", "                    -0.14747395598807245\n", "                ],\n", "                [\n", "                    501,\n", "                    -0.14657434603520514\n", "                ],\n", "                [\n", "                    502,\n", "                    -0.16015357645919792\n", "                ],\n", "                [\n", "                    503,\n", "                    -0.19688769059241906\n", "                ],\n", "                [\n", "                    504,\n", "                    -0.26091712257038324\n", "                ],\n", "                [\n", "                    505,\n", "                    -0.263436852780238\n", "                ],\n", "                [\n", "                    506,\n", "                    -0.22730915590442535\n", "                ],\n", "                [\n", "                    507,\n", "                    -0.19402040016520417\n", "                ],\n", "                [\n", "                    508,\n", "                    -0.1553580371197718\n", "                ],\n", "                [\n", "                    509,\n", "                    -0.11452162120531995\n", "                ],\n", "                [\n", "                    510,\n", "                    -0.12270367003353755\n", "                ],\n", "                [\n", "                    511,\n", "                    -0.15164739865925014\n", "                ],\n", "                [\n", "                    512,\n", "                    -0.16222556326732374\n", "                ],\n", "                [\n", "                    513,\n", "                    -0.16786685390593092\n", "                ],\n", "                [\n", "                    514,\n", "                    -0.14245342800847638\n", "                ],\n", "                [\n", "                    515,\n", "                    -0.14165996409441917\n", "                ],\n", "                [\n", "                    516,\n", "                    -0.16176012810369755\n", "                ],\n", "                [\n", "                    517,\n", "                    -0.17885558277644975\n", "                ],\n", "                [\n", "                    518,\n", "                    -0.21254739959668356\n", "                ],\n", "                [\n", "                    519,\n", "                    -0.23731965038636105\n", "                ],\n", "                [\n", "                    520,\n", "                    -0.26598942350777577\n", "                ],\n", "                [\n", "                    521,\n", "                    -0.27185902440337983\n", "                ],\n", "                [\n", "                    522,\n", "                    -0.292504905168844\n", "                ],\n", "                [\n", "                    523,\n", "                    -0.302953901151259\n", "                ],\n", "                [\n", "                    524,\n", "                    -0.3380013606685228\n", "                ],\n", "                [\n", "                    525,\n", "                    -0.4102692977755691\n", "                ],\n", "                [\n", "                    526,\n", "                    -0.4949205955343583\n", "                ],\n", "                [\n", "                    527,\n", "                    -0.5930955941142155\n", "                ],\n", "                [\n", "                    528,\n", "                    -0.6050207856612335\n", "                ],\n", "                [\n", "                    529,\n", "                    -0.5811442743586266\n", "                ],\n", "                [\n", "                    530,\n", "                    -0.588521431892385\n", "                ],\n", "                [\n", "                    531,\n", "                    -0.6522098438417121\n", "                ],\n", "                [\n", "                    532,\n", "                    -0.6444191712226175\n", "                ],\n", "                [\n", "                    533,\n", "                    -0.6198034685014164\n", "                ],\n", "                [\n", "                    534,\n", "                    -0.5735113541927674\n", "                ],\n", "                [\n", "                    535,\n", "                    -0.5394817905128555\n", "                ],\n", "                [\n", "                    536,\n", "                    -0.4596069837492447\n", "                ],\n", "                [\n", "                    537,\n", "                    -0.40614829487462956\n", "                ],\n", "                [\n", "                    538,\n", "                    -0.3436818201737246\n", "                ],\n", "                [\n", "                    539,\n", "                    -0.30837410557701794\n", "                ],\n", "                [\n", "                    540,\n", "                    -0.2875675017696029\n", "                ],\n", "                [\n", "                    541,\n", "                    -0.28155016258408416\n", "                ],\n", "                [\n", "                    542,\n", "                    -0.23134797191070966\n", "                ],\n", "                [\n", "                    543,\n", "                    -0.17581807355509582\n", "                ],\n", "                [\n", "                    544,\n", "                    -0.10079244864907189\n", "                ],\n", "                [\n", "                    545,\n", "                    0.010191074552501789\n", "                ],\n", "                [\n", "                    546,\n", "                    0.08825291096909105\n", "                ],\n", "                [\n", "                    547,\n", "                    0.15797938780865195\n", "                ],\n", "                [\n", "                    548,\n", "                    0.18288783105550266\n", "                ],\n", "                [\n", "                    549,\n", "                    0.18994843096622027\n", "                ],\n", "                [\n", "                    550,\n", "                    0.1837429359564844\n", "                ],\n", "                [\n", "                    551,\n", "                    0.19593244344507887\n", "                ],\n", "                [\n", "                    552,\n", "                    0.2335631693299316\n", "                ],\n", "                [\n", "                    553,\n", "                    0.21890276175447454\n", "                ],\n", "                [\n", "                    554,\n", "                    0.19774258189615956\n", "                ],\n", "                [\n", "                    555,\n", "                    0.1821014998772963\n", "                ],\n", "                [\n", "                    556,\n", "                    0.1645809769294413\n", "                ],\n", "                [\n", "                    557,\n", "                    0.16892152474033217\n", "                ],\n", "                [\n", "                    558,\n", "                    0.07387299069199393\n", "                ],\n", "                [\n", "                    559,\n", "                    -0.011009741006185791\n", "                ],\n", "                [\n", "                    560,\n", "                    -0.003997525202695584\n", "                ],\n", "                [\n", "                    561,\n", "                    0.0015419319106104723\n", "                ],\n", "                [\n", "                    562,\n", "                    -0.020460395785921293\n", "                ],\n", "                [\n", "                    563,\n", "                    -0.0374655198127094\n", "                ],\n", "                [\n", "                    564,\n", "                    -0.07907961390990614\n", "                ],\n", "                [\n", "                    565,\n", "                    -0.1434886094115857\n", "                ],\n", "                [\n", "                    566,\n", "                    -0.18912541660621685\n", "                ],\n", "                [\n", "                    567,\n", "                    -0.21953459128456032\n", "                ],\n", "                [\n", "                    568,\n", "                    -0.26080064447945084\n", "                ],\n", "                [\n", "                    569,\n", "                    -0.27181193237311696\n", "                ],\n", "                [\n", "                    570,\n", "                    -0.2932958538582042\n", "                ],\n", "                [\n", "                    571,\n", "                    -0.28285395925126977\n", "                ],\n", "                [\n", "                    572,\n", "                    -0.2682586975592134\n", "                ],\n", "                [\n", "                    573,\n", "                    -0.2641369546659025\n", "                ],\n", "                [\n", "                    574,\n", "                    -0.22598872926896618\n", "                ],\n", "                [\n", "                    575,\n", "                    -0.20868184351046715\n", "                ],\n", "                [\n", "                    576,\n", "                    -0.22704617126650994\n", "                ],\n", "                [\n", "                    577,\n", "                    -0.23964447738572936\n", "                ],\n", "                [\n", "                    578,\n", "                    -0.26273836990209\n", "                ],\n", "                [\n", "                    579,\n", "                    -0.2786354267749971\n", "                ],\n", "                [\n", "                    580,\n", "                    -0.2958922613418036\n", "                ],\n", "                [\n", "                    581,\n", "                    -0.29965881536033656\n", "                ],\n", "                [\n", "                    582,\n", "                    -0.3055766703280156\n", "                ],\n", "                [\n", "                    583,\n", "                    -0.3171011815382716\n", "                ],\n", "                [\n", "                    584,\n", "                    -0.3049668107595451\n", "                ],\n", "                [\n", "                    585,\n", "                    -0.2927821406535962\n", "                ],\n", "                [\n", "                    586,\n", "                    -0.2798991980845056\n", "                ],\n", "                [\n", "                    587,\n", "                    -0.2514592949539143\n", "                ],\n", "                [\n", "                    588,\n", "                    -0.22870484896893295\n", "                ],\n", "                [\n", "                    589,\n", "                    -0.2409775212146723\n", "                ],\n", "                [\n", "                    590,\n", "                    -0.23109454338766255\n", "                ],\n", "                [\n", "                    591,\n", "                    -0.1712592240828812\n", "                ],\n", "                [\n", "                    592,\n", "                    -0.1463596522733468\n", "                ],\n", "                [\n", "                    593,\n", "                    -0.12438580367871666\n", "                ],\n", "                [\n", "                    594,\n", "                    -0.11452725217376347\n", "                ],\n", "                [\n", "                    595,\n", "                    -0.10310499954539765\n", "                ],\n", "                [\n", "                    596,\n", "                    -0.11611485860242787\n", "                ],\n", "                [\n", "                    597,\n", "                    -0.11541187229551397\n", "                ],\n", "                [\n", "                    598,\n", "                    -0.11115270214285644\n", "                ],\n", "                [\n", "                    599,\n", "                    -0.0858083072694864\n", "                ],\n", "                [\n", "                    600,\n", "                    -0.053805624023747\n", "                ],\n", "                [\n", "                    601,\n", "                    -0.02732140639219871\n", "                ],\n", "                [\n", "                    602,\n", "                    0.020862198947527943\n", "                ],\n", "                [\n", "                    603,\n", "                    0.05358881624848344\n", "                ],\n", "                [\n", "                    604,\n", "                    0.08021407121363566\n", "                ],\n", "                [\n", "                    605,\n", "                    0.09776703084486726\n", "                ],\n", "                [\n", "                    606,\n", "                    0.0800918051332129\n", "                ],\n", "                [\n", "                    607,\n", "                    0.056556037921708935\n", "                ],\n", "                [\n", "                    608,\n", "                    0.04704447058595207\n", "                ],\n", "                [\n", "                    609,\n", "                    0.03267450221080459\n", "                ],\n", "                [\n", "                    610,\n", "                    0.017852737789127104\n", "                ],\n", "                [\n", "                    611,\n", "                    -0.02986064160437074\n", "                ],\n", "                [\n", "                    612,\n", "                    -0.11476587455820209\n", "                ],\n", "                [\n", "                    613,\n", "                    -0.19034956562392935\n", "                ],\n", "                [\n", "                    614,\n", "                    -0.23543251941475418\n", "                ],\n", "                [\n", "                    615,\n", "                    -0.2656777678900184\n", "                ],\n", "                [\n", "                    616,\n", "                    -0.28873968315821763\n", "                ],\n", "                [\n", "                    617,\n", "                    -0.3338310448850592\n", "                ],\n", "                [\n", "                    618,\n", "                    -0.38130910840454924\n", "                ],\n", "                [\n", "                    619,\n", "                    -0.4133638976758931\n", "                ],\n", "                [\n", "                    620,\n", "                    -0.4265878619463095\n", "                ],\n", "                [\n", "                    621,\n", "                    -0.4416597671502398\n", "                ],\n", "                [\n", "                    622,\n", "                    -0.4412555929692701\n", "                ],\n", "                [\n", "                    623,\n", "                    -0.45186479447455774\n", "                ],\n", "                [\n", "                    624,\n", "                    -0.4757681313294171\n", "                ],\n", "                [\n", "                    625,\n", "                    -0.5018374906165075\n", "                ],\n", "                [\n", "                    626,\n", "                    -0.5348908348942416\n", "                ],\n", "                [\n", "                    627,\n", "                    -0.5443213372782854\n", "                ],\n", "                [\n", "                    628,\n", "                    -0.5335410033936672\n", "                ],\n", "                [\n", "                    629,\n", "                    -0.5277895622773006\n", "                ],\n", "                [\n", "                    630,\n", "                    -0.5236505179578987\n", "                ],\n", "                [\n", "                    631,\n", "                    -0.5192264744830624\n", "                ],\n", "                [\n", "                    632,\n", "                    -0.486709329601263\n", "                ],\n", "                [\n", "                    633,\n", "                    -0.45329322657875437\n", "                ],\n", "                [\n", "                    634,\n", "                    -0.4434852458451246\n", "                ],\n", "                [\n", "                    635,\n", "                    -0.42915152594447115\n", "                ],\n", "                [\n", "                    636,\n", "                    -0.39149232629644537\n", "                ],\n", "                [\n", "                    637,\n", "                    -0.36949161630102445\n", "                ],\n", "                [\n", "                    638,\n", "                    -0.3225167966031748\n", "                ],\n", "                [\n", "                    639,\n", "                    -0.2876218034313194\n", "                ],\n", "                [\n", "                    640,\n", "                    -0.2697682127305061\n", "                ],\n", "                [\n", "                    641,\n", "                    -0.24791975035544667\n", "                ],\n", "                [\n", "                    642,\n", "                    -0.21042685532820116\n", "                ],\n", "                [\n", "                    643,\n", "                    -0.1794517739783288\n", "                ],\n", "                [\n", "                    644,\n", "                    -0.1682952085449152\n", "                ],\n", "                [\n", "                    645,\n", "                    -0.15285009080027656\n", "                ],\n", "                [\n", "                    646,\n", "                    -0.117468872159316\n", "                ],\n", "                [\n", "                    647,\n", "                    -0.09957795872654884\n", "                ],\n", "                [\n", "                    648,\n", "                    -0.09240327773248325\n", "                ],\n", "                [\n", "                    649,\n", "                    -0.08094273532707597\n", "                ],\n", "                [\n", "                    650,\n", "                    -0.07662529913677574\n", "                ],\n", "                [\n", "                    651,\n", "                    -0.08593072321404094\n", "                ],\n", "                [\n", "                    652,\n", "                    -0.08984886773937362\n", "                ],\n", "                [\n", "                    653,\n", "                    -0.06317677165250402\n", "                ],\n", "                [\n", "                    654,\n", "                    -0.02321228871661063\n", "                ],\n", "                [\n", "                    655,\n", "                    -0.009186414180321734\n", "                ],\n", "                [\n", "                    656,\n", "                    0.02344566888007904\n", "                ],\n", "                [\n", "                    657,\n", "                    0.013645213512813115\n", "                ],\n", "                [\n", "                    658,\n", "                    0.0066090138545540356\n", "                ],\n", "                [\n", "                    659,\n", "                    -0.017326572930175033\n", "                ],\n", "                [\n", "                    660,\n", "                    -0.028702584537239062\n", "                ],\n", "                [\n", "                    661,\n", "                    -0.04845641033152326\n", "                ],\n", "                [\n", "                    662,\n", "                    -0.06338084466799643\n", "                ],\n", "                [\n", "                    663,\n", "                    -0.09748539185847527\n", "                ],\n", "                [\n", "                    664,\n", "                    -0.1111287220862458\n", "                ],\n", "                [\n", "                    665,\n", "                    -0.12374239993323322\n", "                ],\n", "                [\n", "                    666,\n", "                    -0.15215776121012325\n", "                ],\n", "                [\n", "                    667,\n", "                    -0.17428195866398077\n", "                ],\n", "                [\n", "                    668,\n", "                    -0.21914528192653115\n", "                ],\n", "                [\n", "                    669,\n", "                    -0.2509995063410475\n", "                ],\n", "                [\n", "                    670,\n", "                    -0.2635234769639183\n", "                ],\n", "                [\n", "                    671,\n", "                    -0.29107332885404347\n", "                ],\n", "                [\n", "                    672,\n", "                    -0.2941842005189095\n", "                ],\n", "                [\n", "                    673,\n", "                    -0.29725757062079694\n", "                ],\n", "                [\n", "                    674,\n", "                    -0.2962779310069408\n", "                ],\n", "                [\n", "                    675,\n", "                    -0.30729071557150256\n", "                ],\n", "                [\n", "                    676,\n", "                    -0.3195965642694194\n", "                ],\n", "                [\n", "                    677,\n", "                    -0.3343707014057422\n", "                ],\n", "                [\n", "                    678,\n", "                    -0.37962826524296567\n", "                ],\n", "                [\n", "                    679,\n", "                    -0.4083670600984206\n", "                ],\n", "                [\n", "                    680,\n", "                    -0.42543173344559015\n", "                ],\n", "                [\n", "                    681,\n", "                    -0.43235782528801003\n", "                ],\n", "                [\n", "                    682,\n", "                    -0.4535978365572788\n", "                ],\n", "                [\n", "                    683,\n", "                    -0.47145143129465694\n", "                ],\n", "                [\n", "                    684,\n", "                    -0.4537418382108971\n", "                ],\n", "                [\n", "                    685,\n", "                    -0.4378868369263458\n", "                ],\n", "                [\n", "                    686,\n", "                    -0.4356313591458427\n", "                ],\n", "                [\n", "                    687,\n", "                    -0.3985863966779739\n", "                ],\n", "                [\n", "                    688,\n", "                    -0.3602339457794681\n", "                ],\n", "                [\n", "                    689,\n", "                    -0.3284736615053738\n", "                ],\n", "                [\n", "                    690,\n", "                    -0.29665607511962655\n", "                ],\n", "                [\n", "                    691,\n", "                    -0.2699425122804264\n", "                ],\n", "                [\n", "                    692,\n", "                    -0.20126445008697402\n", "                ],\n", "                [\n", "                    693,\n", "                    -0.10368175805407631\n", "                ],\n", "                [\n", "                    694,\n", "                    -0.02126019867725759\n", "                ],\n", "                [\n", "                    695,\n", "                    0.028400683440503727\n", "                ],\n", "                [\n", "                    696,\n", "                    0.056614757083302436\n", "                ],\n", "                [\n", "                    697,\n", "                    0.07009741014152482\n", "                ],\n", "                [\n", "                    698,\n", "                    0.06949154105447342\n", "                ],\n", "                [\n", "                    699,\n", "                    0.09694287962097903\n", "                ],\n", "                [\n", "                    700,\n", "                    0.11734557645090682\n", "                ],\n", "                [\n", "                    701,\n", "                    0.12800471981964456\n", "                ],\n", "                [\n", "                    702,\n", "                    0.16680598918467027\n", "                ],\n", "                [\n", "                    703,\n", "                    0.1665869724286697\n", "                ],\n", "                [\n", "                    704,\n", "                    0.2586480051989959\n", "                ],\n", "                [\n", "                    705,\n", "                    0.3310189225523992\n", "                ],\n", "                [\n", "                    706,\n", "                    0.38953153389705975\n", "                ],\n", "                [\n", "                    707,\n", "                    0.41498122615971944\n", "                ],\n", "                [\n", "                    708,\n", "                    0.48044772321838813\n", "                ],\n", "                [\n", "                    709,\n", "                    0.5182867425866498\n", "                ],\n", "                [\n", "                    710,\n", "                    0.5196901139710519\n", "                ],\n", "                [\n", "                    711,\n", "                    0.5316193637568816\n", "                ],\n", "                [\n", "                    712,\n", "                    0.5620298088739073\n", "                ],\n", "                [\n", "                    713,\n", "                    0.5323852310154642\n", "                ],\n", "                [\n", "                    714,\n", "                    0.513462724362661\n", "                ],\n", "                [\n", "                    715,\n", "                    0.4919882507815476\n", "                ],\n", "                [\n", "                    716,\n", "                    0.45918644668487296\n", "                ],\n", "                [\n", "                    717,\n", "                    0.4473994326867974\n", "                ],\n", "                [\n", "                    718,\n", "                    0.4091343996947234\n", "                ],\n", "                [\n", "                    719,\n", "                    0.35215599037392664\n", "                ],\n", "                [\n", "                    720,\n", "                    0.31387198318100573\n", "                ],\n", "                [\n", "                    721,\n", "                    0.28508682674004504\n", "                ],\n", "                [\n", "                    722,\n", "                    0.2616786634117716\n", "                ],\n", "                [\n", "                    723,\n", "                    0.22360470452595393\n", "                ],\n", "                [\n", "                    724,\n", "                    0.21834898511938938\n", "                ],\n", "                [\n", "                    725,\n", "                    0.2141361111100366\n", "                ],\n", "                [\n", "                    726,\n", "                    0.1996202000074394\n", "                ],\n", "                [\n", "                    727,\n", "                    0.19634283147967757\n", "                ],\n", "                [\n", "                    728,\n", "                    0.24019853612055186\n", "                ],\n", "                [\n", "                    729,\n", "                    0.3156957572936534\n", "                ],\n", "                [\n", "                    730,\n", "                    0.38401186211823557\n", "                ],\n", "                [\n", "                    731,\n", "                    0.44432776348087444\n", "                ],\n", "                [\n", "                    732,\n", "                    0.500879221659801\n", "                ],\n", "                [\n", "                    733,\n", "                    0.5107599456272229\n", "                ],\n", "                [\n", "                    734,\n", "                    0.5310282057654909\n", "                ],\n", "                [\n", "                    735,\n", "                    0.5408562932725047\n", "                ],\n", "                [\n", "                    736,\n", "                    0.5647289396865762\n", "                ],\n", "                [\n", "                    737,\n", "                    0.5873672929775253\n", "                ],\n", "                [\n", "                    738,\n", "                    0.5896353214194079\n", "                ],\n", "                [\n", "                    739,\n", "                    0.5958608671055607\n", "                ],\n", "                [\n", "                    740,\n", "                    0.592352552562172\n", "                ],\n", "                [\n", "                    741,\n", "                    0.5860442998280391\n", "                ],\n", "                [\n", "                    742,\n", "                    0.576018810650833\n", "                ],\n", "                [\n", "                    743,\n", "                    0.5488362329689682\n", "                ],\n", "                [\n", "                    744,\n", "                    0.4981508945234161\n", "                ],\n", "                [\n", "                    745,\n", "                    0.44478601802592266\n", "                ],\n", "                [\n", "                    746,\n", "                    0.3755709607355673\n", "                ],\n", "                [\n", "                    747,\n", "                    0.29153556302106587\n", "                ],\n", "                [\n", "                    748,\n", "                    0.2391256581106056\n", "                ],\n", "                [\n", "                    749,\n", "                    0.18177743451282957\n", "                ],\n", "                [\n", "                    750,\n", "                    0.14195443956285736\n", "                ],\n", "                [\n", "                    751,\n", "                    0.09717056101729149\n", "                ],\n", "                [\n", "                    752,\n", "                    0.04821258710247811\n", "                ],\n", "                [\n", "                    753,\n", "                    0.020473860108223718\n", "                ],\n", "                [\n", "                    754,\n", "                    -0.02462598423323925\n", "                ],\n", "                [\n", "                    755,\n", "                    -0.06526402770038864\n", "                ],\n", "                [\n", "                    756,\n", "                    -0.10992045460256605\n", "                ],\n", "                [\n", "                    757,\n", "                    -0.08621913146777516\n", "                ],\n", "                [\n", "                    758,\n", "                    -0.07065576853917577\n", "                ],\n", "                [\n", "                    759,\n", "                    -0.06403882301347252\n", "                ],\n", "                [\n", "                    760,\n", "                    -0.05573165688349491\n", "                ],\n", "                [\n", "                    761,\n", "                    -0.06374476995108047\n", "                ],\n", "                [\n", "                    762,\n", "                    -0.08285766742624112\n", "                ],\n", "                [\n", "                    763,\n", "                    -0.08970842564836268\n", "                ],\n", "                [\n", "                    764,\n", "                    -0.06294239946172375\n", "                ],\n", "                [\n", "                    765,\n", "                    -0.035670528239009514\n", "                ],\n", "                [\n", "                    766,\n", "                    -0.00990857183102456\n", "                ],\n", "                [\n", "                    767,\n", "                    -0.024711499752621435\n", "                ],\n", "                [\n", "                    768,\n", "                    -0.048791155534468444\n", "                ],\n", "                [\n", "                    769,\n", "                    -0.07986449091301573\n", "                ],\n", "                [\n", "                    770,\n", "                    -0.1296243463701341\n", "                ],\n", "                [\n", "                    771,\n", "                    -0.17191911236793267\n", "                ],\n", "                [\n", "                    772,\n", "                    -0.2102763323107304\n", "                ],\n", "                [\n", "                    773,\n", "                    -0.25229094813701813\n", "                ],\n", "                [\n", "                    774,\n", "                    -0.2679742973098982\n", "                ],\n", "                [\n", "                    775,\n", "                    -0.2955555617922414\n", "                ],\n", "                [\n", "                    776,\n", "                    -0.31938070562550536\n", "                ],\n", "                [\n", "                    777,\n", "                    -0.3487664370546941\n", "                ],\n", "                [\n", "                    778,\n", "                    -0.3574445293475943\n", "                ],\n", "                [\n", "                    779,\n", "                    -0.3529906753964749\n", "                ],\n", "                [\n", "                    780,\n", "                    -0.33670357414820273\n", "                ],\n", "                [\n", "                    781,\n", "                    -0.3264877133508577\n", "                ],\n", "                [\n", "                    782,\n", "                    -0.332313033656634\n", "                ],\n", "                [\n", "                    783,\n", "                    -0.32989910473772177\n", "                ],\n", "                [\n", "                    784,\n", "                    -0.3330232447045258\n", "                ],\n", "                [\n", "                    785,\n", "                    -0.3197099840551054\n", "                ],\n", "                [\n", "                    786,\n", "                    -0.3176017600812848\n", "                ],\n", "                [\n", "                    787,\n", "                    -0.3011625332062806\n", "                ],\n", "                [\n", "                    788,\n", "                    -0.2864461869856232\n", "                ],\n", "                [\n", "                    789,\n", "                    -0.27723598767330593\n", "                ],\n", "                [\n", "                    790,\n", "                    -0.2636697518853275\n", "                ],\n", "                [\n", "                    791,\n", "                    -0.24524982638893\n", "                ],\n", "                [\n", "                    792,\n", "                    -0.23919148218076813\n", "                ],\n", "                [\n", "                    793,\n", "                    -0.23650541213527632\n", "                ],\n", "                [\n", "                    794,\n", "                    -0.22532395654823567\n", "                ],\n", "                [\n", "                    795,\n", "                    -0.20362539212479014\n", "                ],\n", "                [\n", "                    796,\n", "                    -0.165159275928362\n", "                ],\n", "                [\n", "                    797,\n", "                    -0.12755580282941104\n", "                ],\n", "                [\n", "                    798,\n", "                    -0.10860657686494157\n", "                ],\n", "                [\n", "                    799,\n", "                    -0.1004998433957418\n", "                ],\n", "                [\n", "                    800,\n", "                    -0.11294613662191644\n", "                ],\n", "                [\n", "                    801,\n", "                    -0.1533192024074168\n", "                ],\n", "                [\n", "                    802,\n", "                    -0.16884432001819505\n", "                ],\n", "                [\n", "                    803,\n", "                    -0.1918472546703054\n", "                ],\n", "                [\n", "                    804,\n", "                    -0.196515112250502\n", "                ],\n", "                [\n", "                    805,\n", "                    -0.17479887403270133\n", "                ],\n", "                [\n", "                    806,\n", "                    -0.14143374296140188\n", "                ],\n", "                [\n", "                    807,\n", "                    -0.09932221584899992\n", "                ],\n", "                [\n", "                    808,\n", "                    -0.03249042434100424\n", "                ],\n", "                [\n", "                    809,\n", "                    0.007477394354330258\n", "                ],\n", "                [\n", "                    810,\n", "                    0.013178934422136024\n", "                ],\n", "                [\n", "                    811,\n", "                    0.018293486504637357\n", "                ],\n", "                [\n", "                    812,\n", "                    0.0037445630763439652\n", "                ],\n", "                [\n", "                    813,\n", "                    0.009055297563280718\n", "                ],\n", "                [\n", "                    814,\n", "                    -0.0036392016000981187\n", "                ],\n", "                [\n", "                    815,\n", "                    -0.02391393226542249\n", "                ],\n", "                [\n", "                    816,\n", "                    -0.03952616931357689\n", "                ],\n", "                [\n", "                    817,\n", "                    -0.06327335025656389\n", "                ],\n", "                [\n", "                    818,\n", "                    -0.07796674443974183\n", "                ],\n", "                [\n", "                    819,\n", "                    -0.1021514066124336\n", "                ],\n", "                [\n", "                    820,\n", "                    -0.13588980111682325\n", "                ],\n", "                [\n", "                    821,\n", "                    -0.1671561808440014\n", "                ],\n", "                [\n", "                    822,\n", "                    -0.1761864648941387\n", "                ],\n", "                [\n", "                    823,\n", "                    -0.19082630037622117\n", "                ],\n", "                [\n", "                    824,\n", "                    -0.20889652703168515\n", "                ],\n", "                [\n", "                    825,\n", "                    -0.2422119863539809\n", "                ],\n", "                [\n", "                    826,\n", "                    -0.2663513121893537\n", "                ],\n", "                [\n", "                    827,\n", "                    -0.25510603969923906\n", "                ],\n", "                [\n", "                    828,\n", "                    -0.24498389128448572\n", "                ],\n", "                [\n", "                    829,\n", "                    -0.2398456394919073\n", "                ],\n", "                [\n", "                    830,\n", "                    -0.22510945202487953\n", "                ],\n", "                [\n", "                    831,\n", "                    -0.19663968176410407\n", "                ],\n", "                [\n", "                    832,\n", "                    -0.1912386972946738\n", "                ],\n", "                [\n", "                    833,\n", "                    -0.1920072855617061\n", "                ],\n", "                [\n", "                    834,\n", "                    -0.19201678023745394\n", "                ],\n", "                [\n", "                    835,\n", "                    -0.21376761760178553\n", "                ],\n", "                [\n", "                    836,\n", "                    -0.21720468257267989\n", "                ],\n", "                [\n", "                    837,\n", "                    -0.2158268311103022\n", "                ],\n", "                [\n", "                    838,\n", "                    -0.22664672718837942\n", "                ],\n", "                [\n", "                    839,\n", "                    -0.23972047223885617\n", "                ],\n", "                [\n", "                    840,\n", "                    -0.24802929844947208\n", "                ],\n", "                [\n", "                    841,\n", "                    -0.26527377142417663\n", "                ],\n", "                [\n", "                    842,\n", "                    -0.2677841274992385\n", "                ],\n", "                [\n", "                    843,\n", "                    -0.2651038181278569\n", "                ],\n", "                [\n", "                    844,\n", "                    -0.2695553840832954\n", "                ],\n", "                [\n", "                    845,\n", "                    -0.26598261799045275\n", "                ],\n", "                [\n", "                    846,\n", "                    -0.2394115605901863\n", "                ],\n", "                [\n", "                    847,\n", "                    -0.22304493088239852\n", "                ],\n", "                [\n", "                    848,\n", "                    -0.21246657506139677\n", "                ],\n", "                [\n", "                    849,\n", "                    -0.20973462591318892\n", "                ],\n", "                [\n", "                    850,\n", "                    -0.20839495491771842\n", "                ],\n", "                [\n", "                    851,\n", "                    -0.20497048385382932\n", "                ],\n", "                [\n", "                    852,\n", "                    -0.1991539269364928\n", "                ],\n", "                [\n", "                    853,\n", "                    -0.18993407263445938\n", "                ],\n", "                [\n", "                    854,\n", "                    -0.16538935404871502\n", "                ],\n", "                [\n", "                    855,\n", "                    -0.14188122006787474\n", "                ],\n", "                [\n", "                    856,\n", "                    -0.1338120983442206\n", "                ],\n", "                [\n", "                    857,\n", "                    -0.13075153746848933\n", "                ],\n", "                [\n", "                    858,\n", "                    -0.12207729134113876\n", "                ],\n", "                [\n", "                    859,\n", "                    -0.11309231455941315\n", "                ],\n", "                [\n", "                    860,\n", "                    -0.10316855993487728\n", "                ],\n", "                [\n", "                    861,\n", "                    -0.10059959480243563\n", "                ],\n", "                [\n", "                    862,\n", "                    -0.06074528200861451\n", "                ],\n", "                [\n", "                    863,\n", "                    -0.03281674313016758\n", "                ],\n", "                [\n", "                    864,\n", "                    -0.0041796628905821365\n", "                ],\n", "                [\n", "                    865,\n", "                    0.058988188156279264\n", "                ],\n", "                [\n", "                    866,\n", "                    0.11259276446756417\n", "                ],\n", "                [\n", "                    867,\n", "                    0.14054401070042388\n", "                ],\n", "                [\n", "                    868,\n", "                    0.15047112372388405\n", "                ],\n", "                [\n", "                    869,\n", "                    0.1804656296684879\n", "                ],\n", "                [\n", "                    870,\n", "                    0.19951584794795174\n", "                ],\n", "                [\n", "                    871,\n", "                    0.2009994565948503\n", "                ],\n", "                [\n", "                    872,\n", "                    0.19508491115757742\n", "                ],\n", "                [\n", "                    873,\n", "                    0.19381186375050952\n", "                ],\n", "                [\n", "                    874,\n", "                    0.1898080589301525\n", "                ],\n", "                [\n", "                    875,\n", "                    0.1629696584705549\n", "                ],\n", "                [\n", "                    876,\n", "                    0.12253536000824283\n", "                ],\n", "                [\n", "                    877,\n", "                    0.09424597676844648\n", "                ],\n", "                [\n", "                    878,\n", "                    0.07100790184531824\n", "                ],\n", "                [\n", "                    879,\n", "                    0.03922869326628309\n", "                ],\n", "                [\n", "                    880,\n", "                    0.013085681493347323\n", "                ],\n", "                [\n", "                    881,\n", "                    -0.026691158858591635\n", "                ],\n", "                [\n", "                    882,\n", "                    -0.05356257327838776\n", "                ],\n", "                [\n", "                    883,\n", "                    -0.08357792562120991\n", "                ],\n", "                [\n", "                    884,\n", "                    -0.1157144218941184\n", "                ],\n", "                [\n", "                    885,\n", "                    -0.13159669764190518\n", "                ],\n", "                [\n", "                    886,\n", "                    -0.11940649547687521\n", "                ],\n", "                [\n", "                    887,\n", "                    -0.12524714625382316\n", "                ],\n", "                [\n", "                    888,\n", "                    -0.14275481115024924\n", "                ]\n", "            ],\n", "            \"hoverAnimation\": true,\n", "            \"label\": {\n", "                \"show\": false,\n", "                \"margin\": 8,\n", "                \"valueAnimation\": false\n", "            },\n", "            \"logBase\": 10,\n", "            \"seriesLayoutBy\": \"column\",\n", "            \"lineStyle\": {\n", "                \"show\": true,\n", "                \"width\": 1,\n", "                \"opacity\": 1,\n", "                \"curveness\": 0,\n", "                \"type\": \"solid\"\n", "            },\n", "            \"areaStyle\": {\n", "                \"opacity\": 0\n", "            },\n", "            \"zlevel\": 0,\n", "            \"z\": 0\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [\n", "                \"DIF\"\n", "            ],\n", "            \"selected\": {}\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"xAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"animation\": true,\n", "            \"animationThreshold\": 2000,\n", "            \"animationDuration\": 1000,\n", "            \"animationEasing\": \"cubicOut\",\n", "            \"animationDelay\": 0,\n", "            \"animationDurationUpdate\": 300,\n", "            \"animationEasingUpdate\": \"cubicOut\",\n", "            \"animationDelayUpdate\": 0,\n", "            \"data\": [\n", "                0,\n", "                1,\n", "                2,\n", "                3,\n", "                4,\n", "                5,\n", "                6,\n", "                7,\n", "                8,\n", "                9,\n", "                10,\n", "                11,\n", "                12,\n", "                13,\n", "                14,\n", "                15,\n", "                16,\n", "                17,\n", "                18,\n", "                19,\n", "                20,\n", "                21,\n", "                22,\n", "                23,\n", "                24,\n", "                25,\n", "                26,\n", "                27,\n", "                28,\n", "                29,\n", "                30,\n", "                31,\n", "                32,\n", "                33,\n", "                34,\n", "                35,\n", "                36,\n", "                37,\n", "                38,\n", "                39,\n", "                40,\n", "                41,\n", "                42,\n", "                43,\n", "                44,\n", "                45,\n", "                46,\n", "                47,\n", "                48,\n", "                49,\n", "                50,\n", "                51,\n", "                52,\n", "                53,\n", "                54,\n", "                55,\n", "                56,\n", "                57,\n", "                58,\n", "                59,\n", "                60,\n", "                61,\n", "                62,\n", "                63,\n", "                64,\n", "                65,\n", "                66,\n", "                67,\n", "                68,\n", "                69,\n", "                70,\n", "                71,\n", "                72,\n", "                73,\n", "                74,\n", "                75,\n", "                76,\n", "                77,\n", "                78,\n", "                79,\n", "                80,\n", "                81,\n", "                82,\n", "                83,\n", "                84,\n", "                85,\n", "                86,\n", "                87,\n", "                88,\n", "                89,\n", "                90,\n", "                91,\n", "                92,\n", "                93,\n", "                94,\n", "                95,\n", "                96,\n", "                97,\n", "                98,\n", "                99,\n", "                100,\n", "                101,\n", "                102,\n", "                103,\n", "                104,\n", "                105,\n", "                106,\n", "                107,\n", "                108,\n", "                109,\n", "                110,\n", "                111,\n", "                112,\n", "                113,\n", "                114,\n", "                115,\n", "                116,\n", "                117,\n", "                118,\n", "                119,\n", "                120,\n", "                121,\n", "                122,\n", "                123,\n", "                124,\n", "                125,\n", "                126,\n", "                127,\n", "                128,\n", "                129,\n", "                130,\n", "                131,\n", "                132,\n", "                133,\n", "                134,\n", "                135,\n", "                136,\n", "                137,\n", "                138,\n", "                139,\n", "                140,\n", "                141,\n", "                142,\n", "                143,\n", "                144,\n", "                145,\n", "                146,\n", "                147,\n", "                148,\n", "                149,\n", "                150,\n", "                151,\n", "                152,\n", "                153,\n", "                154,\n", "                155,\n", "                156,\n", "                157,\n", "                158,\n", "                159,\n", "                160,\n", "                161,\n", "                162,\n", "                163,\n", "                164,\n", "                165,\n", "                166,\n", "                167,\n", "                168,\n", "                169,\n", "                170,\n", "                171,\n", "                172,\n", "                173,\n", "                174,\n", "                175,\n", "                176,\n", "                177,\n", "                178,\n", "                179,\n", "                180,\n", "                181,\n", "                182,\n", "                183,\n", "                184,\n", "                185,\n", "                186,\n", "                187,\n", "                188,\n", "                189,\n", "                190,\n", "                191,\n", "                192,\n", "                193,\n", "                194,\n", "                195,\n", "                196,\n", "                197,\n", "                198,\n", "                199,\n", "                200,\n", "                201,\n", "                202,\n", "                203,\n", "                204,\n", "                205,\n", "                206,\n", "                207,\n", "                208,\n", "                209,\n", "                210,\n", "                211,\n", "                212,\n", "                213,\n", "                214,\n", "                215,\n", "                216,\n", "                217,\n", "                218,\n", "                219,\n", "                220,\n", "                221,\n", "                222,\n", "                223,\n", "                224,\n", "                225,\n", "                226,\n", "                227,\n", "                228,\n", "                229,\n", "                230,\n", "                231,\n", "                232,\n", "                233,\n", "                234,\n", "                235,\n", "                236,\n", "                237,\n", "                238,\n", "                239,\n", "                240,\n", "                241,\n", "                242,\n", "                243,\n", "                244,\n", "                245,\n", "                246,\n", "                247,\n", "                248,\n", "                249,\n", "                250,\n", "                251,\n", "                252,\n", "                253,\n", "                254,\n", "                255,\n", "                256,\n", "                257,\n", "                258,\n", "                259,\n", "                260,\n", "                261,\n", "                262,\n", "                263,\n", "                264,\n", "                265,\n", "                266,\n", "                267,\n", "                268,\n", "                269,\n", "                270,\n", "                271,\n", "                272,\n", "                273,\n", "                274,\n", "                275,\n", "                276,\n", "                277,\n", "                278,\n", "                279,\n", "                280,\n", "                281,\n", "                282,\n", "                283,\n", "                284,\n", "                285,\n", "                286,\n", "                287,\n", "                288,\n", "                289,\n", "                290,\n", "                291,\n", "                292,\n", "                293,\n", "                294,\n", "                295,\n", "                296,\n", "                297,\n", "                298,\n", "                299,\n", "                300,\n", "                301,\n", "                302,\n", "                303,\n", "                304,\n", "                305,\n", "                306,\n", "                307,\n", "                308,\n", "                309,\n", "                310,\n", "                311,\n", "                312,\n", "                313,\n", "                314,\n", "                315,\n", "                316,\n", "                317,\n", "                318,\n", "                319,\n", "                320,\n", "                321,\n", "                322,\n", "                323,\n", "                324,\n", "                325,\n", "                326,\n", "                327,\n", "                328,\n", "                329,\n", "                330,\n", "                331,\n", "                332,\n", "                333,\n", "                334,\n", "                335,\n", "                336,\n", "                337,\n", "                338,\n", "                339,\n", "                340,\n", "                341,\n", "                342,\n", "                343,\n", "                344,\n", "                345,\n", "                346,\n", "                347,\n", "                348,\n", "                349,\n", "                350,\n", "                351,\n", "                352,\n", "                353,\n", "                354,\n", "                355,\n", "                356,\n", "                357,\n", "                358,\n", "                359,\n", "                360,\n", "                361,\n", "                362,\n", "                363,\n", "                364,\n", "                365,\n", "                366,\n", "                367,\n", "                368,\n", "                369,\n", "                370,\n", "                371,\n", "                372,\n", "                373,\n", "                374,\n", "                375,\n", "                376,\n", "                377,\n", "                378,\n", "                379,\n", "                380,\n", "                381,\n", "                382,\n", "                383,\n", "                384,\n", "                385,\n", "                386,\n", "                387,\n", "                388,\n", "                389,\n", "                390,\n", "                391,\n", "                392,\n", "                393,\n", "                394,\n", "                395,\n", "                396,\n", "                397,\n", "                398,\n", "                399,\n", "                400,\n", "                401,\n", "                402,\n", "                403,\n", "                404,\n", "                405,\n", "                406,\n", "                407,\n", "                408,\n", "                409,\n", "                410,\n", "                411,\n", "                412,\n", "                413,\n", "                414,\n", "                415,\n", "                416,\n", "                417,\n", "                418,\n", "                419,\n", "                420,\n", "                421,\n", "                422,\n", "                423,\n", "                424,\n", "                425,\n", "                426,\n", "                427,\n", "                428,\n", "                429,\n", "                430,\n", "                431,\n", "                432,\n", "                433,\n", "                434,\n", "                435,\n", "                436,\n", "                437,\n", "                438,\n", "                439,\n", "                440,\n", "                441,\n", "                442,\n", "                443,\n", "                444,\n", "                445,\n", "                446,\n", "                447,\n", "                448,\n", "                449,\n", "                450,\n", "                451,\n", "                452,\n", "                453,\n", "                454,\n", "                455,\n", "                456,\n", "                457,\n", "                458,\n", "                459,\n", "                460,\n", "                461,\n", "                462,\n", "                463,\n", "                464,\n", "                465,\n", "                466,\n", "                467,\n", "                468,\n", "                469,\n", "                470,\n", "                471,\n", "                472,\n", "                473,\n", "                474,\n", "                475,\n", "                476,\n", "                477,\n", "                478,\n", "                479,\n", "                480,\n", "                481,\n", "                482,\n", "                483,\n", "                484,\n", "                485,\n", "                486,\n", "                487,\n", "                488,\n", "                489,\n", "                490,\n", "                491,\n", "                492,\n", "                493,\n", "                494,\n", "                495,\n", "                496,\n", "                497,\n", "                498,\n", "                499,\n", "                500,\n", "                501,\n", "                502,\n", "                503,\n", "                504,\n", "                505,\n", "                506,\n", "                507,\n", "                508,\n", "                509,\n", "                510,\n", "                511,\n", "                512,\n", "                513,\n", "                514,\n", "                515,\n", "                516,\n", "                517,\n", "                518,\n", "                519,\n", "                520,\n", "                521,\n", "                522,\n", "                523,\n", "                524,\n", "                525,\n", "                526,\n", "                527,\n", "                528,\n", "                529,\n", "                530,\n", "                531,\n", "                532,\n", "                533,\n", "                534,\n", "                535,\n", "                536,\n", "                537,\n", "                538,\n", "                539,\n", "                540,\n", "                541,\n", "                542,\n", "                543,\n", "                544,\n", "                545,\n", "                546,\n", "                547,\n", "                548,\n", "                549,\n", "                550,\n", "                551,\n", "                552,\n", "                553,\n", "                554,\n", "                555,\n", "                556,\n", "                557,\n", "                558,\n", "                559,\n", "                560,\n", "                561,\n", "                562,\n", "                563,\n", "                564,\n", "                565,\n", "                566,\n", "                567,\n", "                568,\n", "                569,\n", "                570,\n", "                571,\n", "                572,\n", "                573,\n", "                574,\n", "                575,\n", "                576,\n", "                577,\n", "                578,\n", "                579,\n", "                580,\n", "                581,\n", "                582,\n", "                583,\n", "                584,\n", "                585,\n", "                586,\n", "                587,\n", "                588,\n", "                589,\n", "                590,\n", "                591,\n", "                592,\n", "                593,\n", "                594,\n", "                595,\n", "                596,\n", "                597,\n", "                598,\n", "                599,\n", "                600,\n", "                601,\n", "                602,\n", "                603,\n", "                604,\n", "                605,\n", "                606,\n", "                607,\n", "                608,\n", "                609,\n", "                610,\n", "                611,\n", "                612,\n", "                613,\n", "                614,\n", "                615,\n", "                616,\n", "                617,\n", "                618,\n", "                619,\n", "                620,\n", "                621,\n", "                622,\n", "                623,\n", "                624,\n", "                625,\n", "                626,\n", "                627,\n", "                628,\n", "                629,\n", "                630,\n", "                631,\n", "                632,\n", "                633,\n", "                634,\n", "                635,\n", "                636,\n", "                637,\n", "                638,\n", "                639,\n", "                640,\n", "                641,\n", "                642,\n", "                643,\n", "                644,\n", "                645,\n", "                646,\n", "                647,\n", "                648,\n", "                649,\n", "                650,\n", "                651,\n", "                652,\n", "                653,\n", "                654,\n", "                655,\n", "                656,\n", "                657,\n", "                658,\n", "                659,\n", "                660,\n", "                661,\n", "                662,\n", "                663,\n", "                664,\n", "                665,\n", "                666,\n", "                667,\n", "                668,\n", "                669,\n", "                670,\n", "                671,\n", "                672,\n", "                673,\n", "                674,\n", "                675,\n", "                676,\n", "                677,\n", "                678,\n", "                679,\n", "                680,\n", "                681,\n", "                682,\n", "                683,\n", "                684,\n", "                685,\n", "                686,\n", "                687,\n", "                688,\n", "                689,\n", "                690,\n", "                691,\n", "                692,\n", "                693,\n", "                694,\n", "                695,\n", "                696,\n", "                697,\n", "                698,\n", "                699,\n", "                700,\n", "                701,\n", "                702,\n", "                703,\n", "                704,\n", "                705,\n", "                706,\n", "                707,\n", "                708,\n", "                709,\n", "                710,\n", "                711,\n", "                712,\n", "                713,\n", "                714,\n", "                715,\n", "                716,\n", "                717,\n", "                718,\n", "                719,\n", "                720,\n", "                721,\n", "                722,\n", "                723,\n", "                724,\n", "                725,\n", "                726,\n", "                727,\n", "                728,\n", "                729,\n", "                730,\n", "                731,\n", "                732,\n", "                733,\n", "                734,\n", "                735,\n", "                736,\n", "                737,\n", "                738,\n", "                739,\n", "                740,\n", "                741,\n", "                742,\n", "                743,\n", "                744,\n", "                745,\n", "                746,\n", "                747,\n", "                748,\n", "                749,\n", "                750,\n", "                751,\n", "                752,\n", "                753,\n", "                754,\n", "                755,\n", "                756,\n", "                757,\n", "                758,\n", "                759,\n", "                760,\n", "                761,\n", "                762,\n", "                763,\n", "                764,\n", "                765,\n", "                766,\n", "                767,\n", "                768,\n", "                769,\n", "                770,\n", "                771,\n", "                772,\n", "                773,\n", "                774,\n", "                775,\n", "                776,\n", "                777,\n", "                778,\n", "                779,\n", "                780,\n", "                781,\n", "                782,\n", "                783,\n", "                784,\n", "                785,\n", "                786,\n", "                787,\n", "                788,\n", "                789,\n", "                790,\n", "                791,\n", "                792,\n", "                793,\n", "                794,\n", "                795,\n", "                796,\n", "                797,\n", "                798,\n", "                799,\n", "                800,\n", "                801,\n", "                802,\n", "                803,\n", "                804,\n", "                805,\n", "                806,\n", "                807,\n", "                808,\n", "                809,\n", "                810,\n", "                811,\n", "                812,\n", "                813,\n", "                814,\n", "                815,\n", "                816,\n", "                817,\n", "                818,\n", "                819,\n", "                820,\n", "                821,\n", "                822,\n", "                823,\n", "                824,\n", "                825,\n", "                826,\n", "                827,\n", "                828,\n", "                829,\n", "                830,\n", "                831,\n", "                832,\n", "                833,\n", "                834,\n", "                835,\n", "                836,\n", "                837,\n", "                838,\n", "                839,\n", "                840,\n", "                841,\n", "                842,\n", "                843,\n", "                844,\n", "                845,\n", "                846,\n", "                847,\n", "                848,\n", "                849,\n", "                850,\n", "                851,\n", "                852,\n", "                853,\n", "                854,\n", "                855,\n", "                856,\n", "                857,\n", "                858,\n", "                859,\n", "                860,\n", "                861,\n", "                862,\n", "                863,\n", "                864,\n", "                865,\n", "                866,\n", "                867,\n", "                868,\n", "                869,\n", "                870,\n", "                871,\n", "                872,\n", "                873,\n", "                874,\n", "                875,\n", "                876,\n", "                877,\n", "                878,\n", "                879,\n", "                880,\n", "                881,\n", "                882,\n", "                883,\n", "                884,\n", "                885,\n", "                886,\n", "                887,\n", "                888\n", "            ]\n", "        }\n", "    ],\n", "    \"yAxis\": [\n", "        {\n", "            \"show\": true,\n", "            \"scale\": false,\n", "            \"nameLocation\": \"end\",\n", "            \"nameGap\": 15,\n", "            \"gridIndex\": 0,\n", "            \"inverse\": false,\n", "            \"offset\": 0,\n", "            \"splitNumber\": 5,\n", "            \"minInterval\": 0,\n", "            \"splitLine\": {\n", "                \"show\": true,\n", "                \"lineStyle\": {\n", "                    \"show\": true,\n", "                    \"width\": 1,\n", "                    \"opacity\": 1,\n", "                    \"curveness\": 0,\n", "                    \"type\": \"solid\"\n", "                }\n", "            },\n", "            \"animation\": true,\n", "            \"animationThreshold\": 2000,\n", "            \"animationDuration\": 1000,\n", "            \"animationEasing\": \"cubicOut\",\n", "            \"animationDelay\": 0,\n", "            \"animationDurationUpdate\": 300,\n", "            \"animationEasingUpdate\": \"cubicOut\",\n", "            \"animationDelayUpdate\": 0\n", "        }\n", "    ]\n", "};\n", "                chart_5f71318954754d7091efe4bcbabe5d8b.setOption(option_5f71318954754d7091efe4bcbabe5d8b);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x20f9d498390>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["L_macd=Line()\n", "L_macd.add_xaxis(x.index.tolist())\n", "L_macd.add_yaxis(\"DIF\",x.tolist(),label_opts=opts.LabelOpts(is_show=False))\n", "L_macd.render_notebook()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Computing indicators...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 1) |                          | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "100% (1 of 1) |##########################| Elapsed Time: 0:00:00 ETA:  00:00:00\n", "100% (1 of 1) |##########################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>my_macd</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001</td>\n", "      <td>2020-01-02</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000001</td>\n", "      <td>2020-01-03</td>\n", "      <td>0.677923</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000001</td>\n", "      <td>2020-01-06</td>\n", "      <td>0.836162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000001</td>\n", "      <td>2020-01-07</td>\n", "      <td>1.177791</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000001</td>\n", "      <td>2020-01-08</td>\n", "      <td>0.036306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>000001</td>\n", "      <td>2023-08-24</td>\n", "      <td>-7.327557</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>000001</td>\n", "      <td>2023-08-25</td>\n", "      <td>-6.566715</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>000001</td>\n", "      <td>2023-08-28</td>\n", "      <td>-4.182659</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>000001</td>\n", "      <td>2023-08-29</td>\n", "      <td>-3.409413</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>000001</td>\n", "      <td>2023-08-30</td>\n", "      <td>-3.526175</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>889 rows × 3 columns</p>\n", "</div>"], "text/plain": ["     symbol       date   my_macd\n", "0    000001 2020-01-02  0.000000\n", "1    000001 2020-01-03  0.677923\n", "2    000001 2020-01-06  0.836162\n", "3    000001 2020-01-07  1.177791\n", "4    000001 2020-01-08  0.036306\n", "..      ...        ...       ...\n", "884  000001 2023-08-24 -7.327557\n", "885  000001 2023-08-25 -6.566715\n", "886  000001 2023-08-28 -4.182659\n", "887  000001 2023-08-29 -3.409413\n", "888  000001 2023-08-30 -3.526175\n", "\n", "[889 rows x 3 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from pybroker import IndicatorSet\n", "from pybroker.indicator import macd\n", "\n", "indicator_set = IndicatorSet()\n", "indicator_set.add(macd(name='my_macd', short_length=12, long_length=26, smoothing=9))\n", "indicator_set(df)"]}], "metadata": {"kernelspec": {"display_name": "quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}