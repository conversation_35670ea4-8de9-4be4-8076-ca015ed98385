import pybroker
from pybroker.ext.data import AKShare
from pybroker import Strategy, StrategyConfig, ExecContext
import matplotlib.pyplot as plt
from pybroker import highv, lowv
import pybroker.indicator

# 设置数据源缓存
pybroker.enable_data_source_cache('akshare')

# 使用indicator


def low_buy_high_sell(ctx: ExecContext):
    high = ctx.indicator('high_10d')[-2]
    low = ctx.indicator('low_10d')[-2]

    # 如果没有持有，且收盘价大于最低价且最低价小于前11天的最低价，则买入
    if not ctx.long_pos() and ctx.close[-1] > ctx.low[-1]*1.05 and ctx.low[-1] < low:
        ctx.buy_shares = ctx.calc_target_shares(1)
        ctx.stop_loss_pct = 8
        ctx.stop_profit_pct = 15
    # 如果持有，且收盘价小于最高价且最高价小于前11天的最高价，则卖出
    if ctx.long_pos() and ctx.close[-1] < ctx.high[-1] * 0.97 and ctx.high[-1] < high:
        ctx.sell_all_shares()
    return

# 自定义indicator


def hhv_10(data, period):
    return highv(data.high, period)

# 自定义indicator


def lwv_10(data, period):
    return lowv(data.low, period)


akshare = AKShare()

# 创建策略，设置初始资金为500000
config = StrategyConfig(initial_cash=500_000)
#  创建策略实例，设置数据源为akshare，开始日期和结束日期，以及策略
strategy = Strategy(data_source=akshare, start_date='1/1/2020',
                    end_date='12/31/2023', config=config)
#  添加执行函数，应用股票代码
strategy.add_execution(low_buy_high_sell, ['000001.SZ'], indicators=[
                       pybroker.indicator('high_10d', hhv_10, period=10), pybroker.indicator('low_10d', lwv_10, period=10)])
# 进行回测，前 20 天作为预热期
result = strategy.backtest(warmup=15)
print(result.trades)
print(result.metrics_df)
chart = plt.subplot2grid((3, 2), (0, 0), rowspan=3, colspan=2)
chart.plot(result.portfolio.index, result.portfolio['market_value'])
plt.show()
