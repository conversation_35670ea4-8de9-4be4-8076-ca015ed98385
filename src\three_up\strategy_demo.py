"""
改进的量化交易策略演示
这个文件展示了如何构建一个能够适应牛熊市场的综合交易策略
"""

import numpy as np
import pandas as pd
from typing import Dict, Tuple

class MarketStateAnalyzer:
    """市场状态分析器"""
    
    @staticmethod
    def identify_market_state(prices: np.array, lookback: int = 60) -> str:
        """
        识别市场状态：牛市、熊市、震荡市
        
        Args:
            prices: 价格序列
            lookback: 回看期间
            
        Returns:
            'bull', 'bear', 'neutral'
        """
        if len(prices) < lookback:
            return 'neutral'
        
        # 计算长期和短期移动平均
        sma_long = np.mean(prices[-lookback:])
        sma_short = np.mean(prices[-lookback//3:])
        
        # 计算价格相对位置
        current_price = prices[-1]
        price_vs_long_ma = (current_price - sma_long) / sma_long
        
        # 计算趋势强度
        trend_strength = (sma_short - sma_long) / sma_long
        
        # 计算波动率
        returns = np.diff(prices[-lookback:]) / prices[-lookback:-1]
        volatility = np.std(returns) * np.sqrt(252)
        
        # 市场状态判断
        if trend_strength > 0.02 and price_vs_long_ma > 0.05:
            return 'bull'  # 牛市
        elif trend_strength < -0.02 and price_vs_long_ma < -0.05:
            return 'bear'  # 熊市
        else:
            return 'neutral'  # 震荡市

class RiskManager:
    """风险管理器"""
    
    @staticmethod
    def calculate_position_size(market_state: str, volatility: float) -> float:
        """
        根据市场状态和波动率计算仓位大小
        
        Args:
            market_state: 市场状态
            volatility: 波动率
            
        Returns:
            仓位比例 (0-1)
        """
        base_position = 0.3  # 基础仓位30%
        
        if market_state == 'bull':
            position_multiplier = 1.5  # 牛市增加仓位
        elif market_state == 'bear':
            position_multiplier = 0.5  # 熊市减少仓位
        else:
            position_multiplier = 1.0  # 震荡市正常仓位
        
        # 根据波动率调整
        if volatility > 0.3:  # 高波动
            volatility_multiplier = 0.7
        elif volatility < 0.15:  # 低波动
            volatility_multiplier = 1.2
        else:
            volatility_multiplier = 1.0
        
        final_position = base_position * position_multiplier * volatility_multiplier
        return min(final_position, 0.8)  # 最大仓位80%
    
    @staticmethod
    def get_dynamic_stops(market_state: str, volatility: float) -> Tuple[float, float]:
        """
        根据市场状态动态设置止损止盈
        
        Args:
            market_state: 市场状态
            volatility: 波动率
            
        Returns:
            (止损百分比, 止盈百分比)
        """
        if market_state == 'bull':
            stop_loss = max(3, min(8, volatility * 20))  # 牛市较小止损
            stop_profit = max(15, min(25, volatility * 50))  # 牛市较大止盈
        elif market_state == 'bear':
            stop_loss = max(5, min(12, volatility * 25))  # 熊市较大止损
            stop_profit = max(8, min(15, volatility * 30))  # 熊市较小止盈
        else:
            stop_loss = max(4, min(10, volatility * 22))  # 震荡市中等止损
            stop_profit = max(10, min(20, volatility * 40))  # 震荡市中等止盈
        
        return stop_loss, stop_profit

class TechnicalIndicators:
    """技术指标计算器"""
    
    @staticmethod
    def sma(prices: np.array, period: int) -> float:
        """简单移动平均"""
        if len(prices) < period:
            return prices[-1]
        return np.mean(prices[-period:])
    
    @staticmethod
    def rsi(prices: np.array, period: int = 14) -> float:
        """相对强弱指数"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = np.diff(prices[-period-1:])
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    @staticmethod
    def bollinger_bands(prices: np.array, period: int = 20, std_dev: float = 2) -> Tuple[float, float, float]:
        """布林带"""
        if len(prices) < period:
            return prices[-1], prices[-1], prices[-1]
        
        sma = np.mean(prices[-period:])
        std = np.std(prices[-period:])
        
        upper = sma + (std_dev * std)
        lower = sma - (std_dev * std)
        
        return upper, sma, lower

class AdvancedTradingStrategy:
    """改进的交易策略"""
    
    def __init__(self):
        self.market_analyzer = MarketStateAnalyzer()
        self.risk_manager = RiskManager()
        self.indicators = TechnicalIndicators()
        self.position = 0  # 当前仓位
        self.entry_price = 0  # 入场价格
    
    def generate_signal(self, data: Dict) -> Dict:
        """
        生成交易信号
        
        Args:
            data: 包含价格、成交量等数据的字典
            
        Returns:
            交易信号字典
        """
        prices = data['close']
        volumes = data.get('volume', np.ones_like(prices))
        
        if len(prices) < 100:
            return {'action': 'hold', 'reason': '数据不足'}
        
        # 1. 识别市场状态
        market_state = self.market_analyzer.identify_market_state(prices)
        
        # 2. 计算技术指标
        sma_5 = self.indicators.sma(prices, 5)
        sma_20 = self.indicators.sma(prices, 20)
        sma_60 = self.indicators.sma(prices, 60)
        rsi = self.indicators.rsi(prices)
        bb_upper, bb_middle, bb_lower = self.indicators.bollinger_bands(prices)
        
        # 3. 计算波动率
        returns = np.diff(prices[-60:]) / prices[-60:-1]
        volatility = np.std(returns) * np.sqrt(252)
        
        current_price = prices[-1]
        
        # 4. 生成买入信号
        if self.position == 0:
            # 基础趋势条件
            trend_up = sma_5 > sma_20 > sma_60
            price_above_ma = current_price > sma_20
            
            # 动量条件
            rsi_condition = 30 < rsi < 70
            
            # 成交量条件（简化）
            volume_surge = volumes[-1] > np.mean(volumes[-20:]) * 1.2
            
            # 布林带条件
            bb_condition = bb_lower < current_price < bb_upper
            
            # 根据市场状态调整买入条件
            if market_state == 'bull':
                buy_signal = trend_up and (rsi_condition or volume_surge)
            elif market_state == 'bear':
                buy_signal = trend_up and rsi_condition and volume_surge and bb_condition
            else:
                buy_signal = price_above_ma and rsi_condition and volume_surge
            
            if buy_signal:
                position_size = self.risk_manager.calculate_position_size(market_state, volatility)
                stop_loss, stop_profit = self.risk_manager.get_dynamic_stops(market_state, volatility)
                
                return {
                    'action': 'buy',
                    'position_size': position_size,
                    'stop_loss': stop_loss,
                    'stop_profit': stop_profit,
                    'market_state': market_state,
                    'volatility': volatility,
                    'reason': f'{market_state}市场买入信号'
                }
        
        # 5. 生成卖出信号
        elif self.position > 0:
            # 趋势转弱信号
            trend_weak = sma_5 < sma_20 or current_price < sma_20
            
            # 动量转弱
            rsi_overbought = rsi > 75
            
            # 布林带超买
            bb_overbought = current_price > bb_upper
            
            # 根据市场状态调整卖出条件
            if market_state == 'bull':
                sell_signal = rsi_overbought or bb_overbought
            elif market_state == 'bear':
                sell_signal = trend_weak or rsi_overbought
            else:
                sell_signal = trend_weak or rsi_overbought
            
            if sell_signal:
                return {
                    'action': 'sell',
                    'market_state': market_state,
                    'reason': f'{market_state}市场卖出信号'
                }
        
        return {
            'action': 'hold',
            'market_state': market_state,
            'rsi': rsi,
            'volatility': volatility,
            'reason': '无明确信号'
        }

def demo_strategy():
    """策略演示"""
    print("=== 改进的量化交易策略演示 ===\n")
    
    # 模拟价格数据（包含牛市、熊市、震荡市）
    np.random.seed(42)
    
    # 生成模拟价格序列
    n_days = 500
    base_price = 100
    
    # 牛市阶段 (前150天)
    bull_trend = np.cumsum(np.random.normal(0.001, 0.02, 150))
    bull_prices = base_price * np.exp(bull_trend)
    
    # 震荡市阶段 (中间200天)
    sideways_prices = bull_prices[-1] + np.cumsum(np.random.normal(0, 0.015, 200))
    
    # 熊市阶段 (后150天)
    bear_trend = np.cumsum(np.random.normal(-0.001, 0.025, 150))
    bear_prices = sideways_prices[-1] * np.exp(bear_trend)
    
    prices = np.concatenate([bull_prices, sideways_prices, bear_prices])
    volumes = np.random.normal(1000000, 200000, n_days)
    
    # 创建策略实例
    strategy = AdvancedTradingStrategy()
    
    # 模拟交易
    signals = []
    for i in range(100, n_days):
        data = {
            'close': prices[:i+1],
            'volume': volumes[:i+1]
        }
        
        signal = strategy.generate_signal(data)
        signal['day'] = i
        signal['price'] = prices[i]
        signals.append(signal)
        
        # 更新仓位
        if signal['action'] == 'buy':
            strategy.position = signal['position_size']
            strategy.entry_price = prices[i]
        elif signal['action'] == 'sell':
            strategy.position = 0
            strategy.entry_price = 0
    
    # 分析结果
    buy_signals = [s for s in signals if s['action'] == 'buy']
    sell_signals = [s for s in signals if s['action'] == 'sell']
    
    print(f"总交易天数: {len(signals)}")
    print(f"买入信号数量: {len(buy_signals)}")
    print(f"卖出信号数量: {len(sell_signals)}")
    
    print("\n=== 市场状态分布 ===")
    market_states = [s['market_state'] for s in signals if 'market_state' in s]
    for state in ['bull', 'bear', 'neutral']:
        count = market_states.count(state)
        percentage = count / len(market_states) * 100
        print(f"{state}市场: {count}天 ({percentage:.1f}%)")
    
    print("\n=== 买入信号示例 ===")
    for signal in buy_signals[:5]:
        print(f"第{signal['day']}天: {signal['reason']}, "
              f"仓位{signal['position_size']:.1%}, "
              f"止损{signal['stop_loss']:.1f}%, "
              f"止盈{signal['stop_profit']:.1f}%")

if __name__ == "__main__":
    demo_strategy()
