{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 基于Pybroker框架的量化交易策略\n", "\n", "* 量化交易策略平台：掘金，聚宽，米筐，优矿等\n", "* 常见的量化交易策略库：Zipline、Backtrader、PyBroker等\n", "\n", "## 什么是量化交易策略\n", "\n", "量化交易策略是指通过数学模型和算法，对金融市场的价格、趋势、波动等数据进行挖掘和分析，从而制定出买卖股票、期货、外汇等金融产品的交易策略。\n", "\n", "## pybroker框架\n", "\n", "pybroker是一个基于Python的量化交易策略框架，它提供了一套完整的量化交易策略开发、回测、模拟和实盘交易的功能。\n", "\n", "https://www.pybroker.com/zh-cn/latest/index.html\n", "\n", "https://github.com/edtechre/pybroker/tree/master\n", "\n", "* 库很新，学习资源少，除了官方文档，没有其他学习资料，借助大模型阅读源代码进行提问学习会有帮助\n", "* 使用简单，速度快，支持在线akshare数据源也支持离线数据，对机器学习模型支持友好\n", "\n", "## pybroker安装（本课程第一次课大部分同学已经完成安装不需要再走下面的流程）\n", "\n", "* 直接pip通常可以：pip install lib-pybroker -i https://pypi.tuna.tsinghua.edu.cn/simple\n", "* 建议先创建虚拟环境在再在虚拟环境中装库（如果直接装好能用那就不用搞虚拟环境）\n", "* 如果想要和talib结合，某些版本因为numpy版本冲突，可能需要先安装ta-lib再安装pybroker\n", "\n", "ta_lib安装需要下载对应版本的ta_lib库\n", "\n", "https://github.com/cgohlke/talib-build/releases\n", "\n", "## pybroker与akshare的使用\n", "\n", "* 由于akshare是一个爬虫接口，对应数据源通常会不稳定，一旦出现异常常用手段就是保持更新\n", "\n", "* pip install --upgrade lib-pybroker akshare -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## pybroker 的数据形式与离线保存\n", "\n", "* pybroker对数据的要求\n", "\n", "* 离线保存后，pybroker会自动读取离线数据，避免总是网络获取"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["<diskcache.core.Cache at 0x27a9b21a0d0>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pybroker\n", "from pybroker import ExecContext, StrategyConfig, Strategy\n", "from pybroker.ext.data import AKShare\n", "import matplotlib.pyplot as plt\n", "\n", "akshare = AKShare()\n", "\n", "pybroker.enable_data_source_cache('akshare')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded cached bar data.\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>486</th>\n", "      <td>2021-03-02</td>\n", "      <td>600000.SH</td>\n", "      <td>93.71</td>\n", "      <td>94.30</td>\n", "      <td>92.06</td>\n", "      <td>92.79</td>\n", "      <td>747631</td>\n", "    </tr>\n", "    <tr>\n", "      <th>487</th>\n", "      <td>2021-03-03</td>\n", "      <td>600000.SH</td>\n", "      <td>92.65</td>\n", "      <td>95.75</td>\n", "      <td>92.52</td>\n", "      <td>95.75</td>\n", "      <td>1135709</td>\n", "    </tr>\n", "    <tr>\n", "      <th>488</th>\n", "      <td>2021-03-04</td>\n", "      <td>600000.SH</td>\n", "      <td>94.96</td>\n", "      <td>95.75</td>\n", "      <td>94.70</td>\n", "      <td>95.49</td>\n", "      <td>754129</td>\n", "    </tr>\n", "    <tr>\n", "      <th>489</th>\n", "      <td>2021-03-05</td>\n", "      <td>600000.SH</td>\n", "      <td>95.42</td>\n", "      <td>95.89</td>\n", "      <td>94.24</td>\n", "      <td>95.36</td>\n", "      <td>721548</td>\n", "    </tr>\n", "    <tr>\n", "      <th>490</th>\n", "      <td>2021-03-08</td>\n", "      <td>600000.SH</td>\n", "      <td>96.15</td>\n", "      <td>96.41</td>\n", "      <td>94.37</td>\n", "      <td>94.63</td>\n", "      <td>801531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>481</th>\n", "      <td>2023-02-23</td>\n", "      <td>000001.SZ</td>\n", "      <td>2481.29</td>\n", "      <td>2533.30</td>\n", "      <td>2478.04</td>\n", "      <td>2489.42</td>\n", "      <td>824491</td>\n", "    </tr>\n", "    <tr>\n", "      <th>482</th>\n", "      <td>2023-02-24</td>\n", "      <td>000001.SZ</td>\n", "      <td>2484.54</td>\n", "      <td>2486.17</td>\n", "      <td>2448.78</td>\n", "      <td>2458.54</td>\n", "      <td>729989</td>\n", "    </tr>\n", "    <tr>\n", "      <th>483</th>\n", "      <td>2023-02-27</td>\n", "      <td>000001.SZ</td>\n", "      <td>2440.66</td>\n", "      <td>2461.79</td>\n", "      <td>2429.28</td>\n", "      <td>2430.91</td>\n", "      <td>621462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>484</th>\n", "      <td>2023-02-28</td>\n", "      <td>000001.SZ</td>\n", "      <td>2440.66</td>\n", "      <td>2456.91</td>\n", "      <td>2417.90</td>\n", "      <td>2445.53</td>\n", "      <td>607936</td>\n", "    </tr>\n", "    <tr>\n", "      <th>485</th>\n", "      <td>2023-03-01</td>\n", "      <td>000001.SZ</td>\n", "      <td>2448.78</td>\n", "      <td>2512.17</td>\n", "      <td>2439.03</td>\n", "      <td>2508.92</td>\n", "      <td>1223452</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>972 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          date     symbol     open     high      low    close   volume\n", "486 2021-03-02  600000.SH    93.71    94.30    92.06    92.79   747631\n", "487 2021-03-03  600000.SH    92.65    95.75    92.52    95.75  1135709\n", "488 2021-03-04  600000.SH    94.96    95.75    94.70    95.49   754129\n", "489 2021-03-05  600000.SH    95.42    95.89    94.24    95.36   721548\n", "490 2021-03-08  600000.SH    96.15    96.41    94.37    94.63   801531\n", "..         ...        ...      ...      ...      ...      ...      ...\n", "481 2023-02-23  000001.SZ  2481.29  2533.30  2478.04  2489.42   824491\n", "482 2023-02-24  000001.SZ  2484.54  2486.17  2448.78  2458.54   729989\n", "483 2023-02-27  000001.SZ  2440.66  2461.79  2429.28  2430.91   621462\n", "484 2023-02-28  000001.SZ  2440.66  2456.91  2417.90  2445.53   607936\n", "485 2023-03-01  000001.SZ  2448.78  2512.17  2439.03  2508.92  1223452\n", "\n", "[972 rows x 7 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 获取股票数据\n", "# 你可以用000001替换000001.SZ，程序仍然可以正常运行！\n", "# 并且你可以将起始日期设置为“20210301”这种格式。\n", "# 你还可以将“adjust”设置为“qfq”（前复权）或“hfq”（后复权）来调整数据，\n", "# 并将“timeframe”设置为“1d”（日数据）、“1w”（周数据）以获取每日、每周的数据。 \n", "df = akshare.query(\n", "    symbols=['000001.SZ', '600000.SH'],\n", "    start_date='3/2/2021',\n", "    end_date='3/1/2023',\n", "    adjust=\"hfq\",\n", "    timeframe=\"1d\",\n", ")\n", "df"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded cached bar data.\n", "\n"]}], "source": ["df = akshare.query(symbols='000001', start_date='20200101', end_date='20230830', adjust='')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>symbol</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2020-01-02</td>\n", "      <td>000001</td>\n", "      <td>16.65</td>\n", "      <td>16.95</td>\n", "      <td>16.55</td>\n", "      <td>16.87</td>\n", "      <td>1530232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2020-01-03</td>\n", "      <td>000001</td>\n", "      <td>16.94</td>\n", "      <td>17.31</td>\n", "      <td>16.92</td>\n", "      <td>17.18</td>\n", "      <td>1116195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2020-01-06</td>\n", "      <td>000001</td>\n", "      <td>17.01</td>\n", "      <td>17.34</td>\n", "      <td>16.91</td>\n", "      <td>17.07</td>\n", "      <td>862084</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2020-01-07</td>\n", "      <td>000001</td>\n", "      <td>17.13</td>\n", "      <td>17.28</td>\n", "      <td>16.95</td>\n", "      <td>17.15</td>\n", "      <td>728608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2020-01-08</td>\n", "      <td>000001</td>\n", "      <td>17.00</td>\n", "      <td>17.05</td>\n", "      <td>16.63</td>\n", "      <td>16.66</td>\n", "      <td>847824</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>2023-08-24</td>\n", "      <td>000001</td>\n", "      <td>11.29</td>\n", "      <td>11.32</td>\n", "      <td>11.05</td>\n", "      <td>11.13</td>\n", "      <td>1291271</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>2023-08-25</td>\n", "      <td>000001</td>\n", "      <td>11.10</td>\n", "      <td>11.33</td>\n", "      <td>11.08</td>\n", "      <td>11.23</td>\n", "      <td>845350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>2023-08-28</td>\n", "      <td>000001</td>\n", "      <td>11.84</td>\n", "      <td>11.93</td>\n", "      <td>11.49</td>\n", "      <td>11.52</td>\n", "      <td>1997610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>2023-08-29</td>\n", "      <td>000001</td>\n", "      <td>11.47</td>\n", "      <td>11.49</td>\n", "      <td>11.13</td>\n", "      <td>11.31</td>\n", "      <td>1964961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>2023-08-30</td>\n", "      <td>000001</td>\n", "      <td>11.27</td>\n", "      <td>11.27</td>\n", "      <td>11.13</td>\n", "      <td>11.13</td>\n", "      <td>1375638</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>889 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          date  symbol   open   high    low  close   volume\n", "0   2020-01-02  000001  16.65  16.95  16.55  16.87  1530232\n", "1   2020-01-03  000001  16.94  17.31  16.92  17.18  1116195\n", "2   2020-01-06  000001  17.01  17.34  16.91  17.07   862084\n", "3   2020-01-07  000001  17.13  17.28  16.95  17.15   728608\n", "4   2020-01-08  000001  17.00  17.05  16.63  16.66   847824\n", "..         ...     ...    ...    ...    ...    ...      ...\n", "884 2023-08-24  000001  11.29  11.32  11.05  11.13  1291271\n", "885 2023-08-25  000001  11.10  11.33  11.08  11.23   845350\n", "886 2023-08-28  000001  11.84  11.93  11.49  11.52  1997610\n", "887 2023-08-29  000001  11.47  11.49  11.13  11.31  1964961\n", "888 2023-08-30  000001  11.27  11.27  11.13  11.13  1375638\n", "\n", "[889 rows x 7 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第一个量化策略\n", "\n", "* 数据的导入\n", "\n", "* 策略函数的编写与运行机制：每天每个股票都会运行一次\n", "\n", "* 策略的结果分析"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2023-08-01 00:00:00 to 2023-08-30 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2023-08-01 00:00:00 to 2023-08-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 22) |                         | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "100% (22 of 22) |########################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2023-08-01 00:00:00\n", "2023-08-01 00:00:00\n", "2023-08-02 00:00:00\n", "2023-08-02 00:00:00\n", "2023-08-03 00:00:00\n", "2023-08-03 00:00:00\n", "2023-08-04 00:00:00\n", "2023-08-04 00:00:00\n", "2023-08-07 00:00:00\n", "2023-08-07 00:00:00\n", "2023-08-08 00:00:00\n", "2023-08-08 00:00:00\n", "2023-08-09 00:00:00\n", "2023-08-09 00:00:00\n", "2023-08-10 00:00:00\n", "2023-08-10 00:00:00\n", "2023-08-11 00:00:00\n", "2023-08-11 00:00:00\n", "2023-08-14 00:00:00\n", "2023-08-14 00:00:00\n", "2023-08-15 00:00:00\n", "2023-08-15 00:00:00\n", "2023-08-16 00:00:00\n", "2023-08-16 00:00:00\n", "2023-08-17 00:00:00\n", "2023-08-17 00:00:00\n", "2023-08-18 00:00:00\n", "2023-08-18 00:00:00\n", "2023-08-21 00:00:00\n", "2023-08-21 00:00:00\n", "2023-08-22 00:00:00\n", "2023-08-22 00:00:00\n", "2023-08-23 00:00:00\n", "2023-08-23 00:00:00\n", "2023-08-24 00:00:00\n", "2023-08-24 00:00:00\n", "2023-08-25 00:00:00\n", "2023-08-25 00:00:00\n", "2023-08-28 00:00:00\n", "2023-08-28 00:00:00\n", "2023-08-29 00:00:00\n", "2023-08-29 00:00:00\n", "2023-08-30 00:00:00\n", "2023-08-30 00:00:00\n", "\n", "Finished backtest: 0:00:00\n"]}], "source": ["\n", "# 定义交易策略函数\n", "def buy_func(ctx: ExecContext) -> None:\n", "    pos = ctx.long_pos()  # 获取当前的长期持有的股票\n", "    print(ctx.dt)\n", "    #print(ctx.date[-1])\n", "    #print(ctx.volume)\n", "    #print(ctx.symbol)\n", "    if pos:  # 如果当前持有股票\n", "        #ctx.sell_shares = pos.shares  # 卖出所有的股票\n", "        pass\n", "    else:  # 如果当前没有持有股票\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)  # 买入全部可购买的股票\n", "        ctx.hold_bars = 3  # 设置持有的交易日为3天\n", "\n", "\n", "# 创建策略配置对象，设置初始现金为 500,000 元\n", "my_config = StrategyConfig(initial_cash=500000)\n", "\n", "# 创建策略对象，设置数据源为 AKShare，开始日期为 '20230801'，结束日期为 '20230830'，策略配置为 my_config\n", "strategy = Strategy(data_source=akshare, start_date='20230801', end_date='20230830', config=my_config)\n", "\n", "# 将定义的交易策略函数添加到策略对象中，应用于股票 '000001'\n", "strategy.add_execution(fn=buy_func, symbols=['000001',\"600000\"])\n", "\n", "# 执行回测\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='date'>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result.portfolio.equity.plot()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x22e4080e550>]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "chart = plt.subplot2grid((5, 2), (0, 0), rowspan=3, colspan=2)\n", "plt.xticks(rotation=30)\n", "chart.plot(result.portfolio.index, result.portfolio['market_value'])\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["#result.orders"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["#result.portfolio"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#result.positions"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#result.trades"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>500000.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>488816.610000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>-8620.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>-2563.140000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>total_return_pct</td>\n", "      <td>-1.724050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>total_profit</td>\n", "      <td>8971.780000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>total_loss</td>\n", "      <td>-17592.030000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>total_fees</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>max_drawdown</td>\n", "      <td>-24678.790000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>max_drawdown_pct</td>\n", "      <td>-4.869287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>win_rate</td>\n", "      <td>40.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>loss_rate</td>\n", "      <td>60.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>winning_trades</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>losing_trades</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>avg_pnl</td>\n", "      <td>-862.025000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>avg_return_pct</td>\n", "      <td>-0.341000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>avg_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>avg_profit</td>\n", "      <td>2242.945000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>avg_profit_pct</td>\n", "      <td>0.920000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>avg_winning_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>avg_loss</td>\n", "      <td>-2932.005000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>avg_loss_pct</td>\n", "      <td>-1.181667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>avg_losing_trade_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>largest_win</td>\n", "      <td>3455.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>largest_win_pct</td>\n", "      <td>1.420000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>largest_win_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>largest_loss</td>\n", "      <td>-6624.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>largest_loss_pct</td>\n", "      <td>-2.660000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>largest_loss_bars</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>max_wins</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>max_losses</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>sharpe</td>\n", "      <td>-0.117967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>sortino</td>\n", "      <td>-0.250087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>profit_factor</td>\n", "      <td>0.746119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>ulcer_index</td>\n", "      <td>3.617931</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>upi</td>\n", "      <td>-0.028605</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>equity_r2</td>\n", "      <td>0.591262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>std_error</td>\n", "      <td>7674.257024</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      name          value\n", "0              trade_count      10.000000\n", "1     initial_market_value  500000.000000\n", "2         end_market_value  488816.610000\n", "3                total_pnl   -8620.250000\n", "4           unrealized_pnl   -2563.140000\n", "5         total_return_pct      -1.724050\n", "6             total_profit    8971.780000\n", "7               total_loss  -17592.030000\n", "8               total_fees       0.000000\n", "9             max_drawdown  -24678.790000\n", "10        max_drawdown_pct      -4.869287\n", "11                win_rate      40.000000\n", "12               loss_rate      60.000000\n", "13          winning_trades       4.000000\n", "14           losing_trades       6.000000\n", "15                 avg_pnl    -862.025000\n", "16          avg_return_pct      -0.341000\n", "17          avg_trade_bars       3.000000\n", "18              avg_profit    2242.945000\n", "19          avg_profit_pct       0.920000\n", "20  avg_winning_trade_bars       3.000000\n", "21                avg_loss   -2932.005000\n", "22            avg_loss_pct      -1.181667\n", "23   avg_losing_trade_bars       3.000000\n", "24             largest_win    3455.200000\n", "25         largest_win_pct       1.420000\n", "26        largest_win_bars       3.000000\n", "27            largest_loss   -6624.700000\n", "28        largest_loss_pct      -2.660000\n", "29       largest_loss_bars       3.000000\n", "30                max_wins       2.000000\n", "31              max_losses       6.000000\n", "32                  sharpe      -0.117967\n", "33                 sortino      -0.250087\n", "34           profit_factor       0.746119\n", "35             ulcer_index       3.617931\n", "36                     upi      -0.028605\n", "37               equity_r2       0.591262\n", "38               std_error    7674.257024"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### classwork1\n", "\n", "设定，时间2023年整年，初始金额1000000，编写如下策略：\n", "\n", "\n", "* 导入002594 比亚迪单个股票数据，一开始就购买一直持有到最后\n", "\n", "* 导入002594 比亚迪和'000001' 股票数据，一开始就购买各50%，每次持有2天 \n", "\n", "查看策略的收益曲线与各项指标\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 基本交易操作：买入，卖出，止损，止盈"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading bar data...\n", "Loaded bar data: 0:00:01 \n", "\n"]}], "source": ["df0 = akshare.query(symbols='000001', start_date='20200101', end_date='20230101', adjust='')\n", "#df0.plot()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df0.close.plot()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading bar data...\n", "Loaded bar data: 0:00:00 \n", "\n"]}], "source": ["df1 = akshare.query(symbols='002594', start_date='20200101', end_date='20230101', adjust='')\n", "#df0.plot()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["df1.close.plot()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loading bar data...\n", "Loaded bar data: 0:00:00 \n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 11% (81 of 728) |##                     | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 24% (181 of 728) |#####                 | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 38% (281 of 728) |########              | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 52% (381 of 728) |###########           | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 64% (471 of 728) |##############        | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 75% (551 of 728) |################      | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 89% (651 of 728) |###################   | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 97% (711 of 728) |##################### | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:01\n"]}], "source": ["strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(buy_by_proportion, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>1185</td>\n", "      <td>NaN</td>\n", "      <td>17.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>415</td>\n", "      <td>NaN</td>\n", "      <td>48.34</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                  \n", "1   buy  000001.SZ 2020-01-03    1185          NaN       17.12   0.0\n", "2   buy  002594.SZ 2020-01-03     415          NaN       48.34   0.0"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='date'>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["result.portfolio.equity.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 买入操作\n", " * 固定数量买入：通过`ExecContext.buy_shares`属性设置买入股票数量。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def buy_fixed_shares(ctx):\n", "    if not ctx.long_pos():\n", "        ctx.buy_shares = 100"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 按资金比例买入：利用`ExecContext.calc_target_shares`方法，根据目标资金比例计算买入数量。"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["def buy_by_proportion(ctx):\n", "    if not ctx.long_pos():\n", "        target_size = 0.2  # 使用20%资金\n", "        ctx.buy_shares = ctx.calc_target_shares(target_size)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 卖出操作\n", "* 卖出所有多头股票：使用`ExecContext.sell_all_shares()`方法平仓所有多头头寸。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def sell_all_long_shares(ctx):\n", "    if ctx.long_pos():\n", "        ctx.sell_all_shares()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 按数量卖出：通过`ExecContext.sell_shares`属性设置卖出股票数量。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sell_fixed_shares(ctx):\n", "    if ctx.long_pos():\n", "        ctx.sell_shares = 50"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 止损操作\n", "* 百分比止损：借助`ExecContext.stop_loss_pct`属性，按入场价格的百分比设置止损。"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def buy_with_percentage_stop_loss(ctx):\n", "    if not ctx.long_pos():\n", "        ctx.buy_shares = ctx.calc_target_shares(1)\n", "        ctx.stop_loss_pct = 15  # 入场价下跌15%止损"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 移动止损：利用`ExecContext.stop_trailing_pct`属性，当证券价格跌破最高市场价格一定百分比时退出交易。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def buy_with_trailing_stop_loss(ctx):\n", "    if not ctx.long_pos():\n", "        ctx.buy_shares = ctx.calc_target_shares(1)\n", "        ctx.stop_trailing_pct = 10  # 最高市场价下跌10%止损"]}, {"cell_type": "markdown", "metadata": {}, "source": ["* 设定限价与止损结合：同时设置止损和限价，确保订单在特定价格执行。\n", "\n", "ctx.stop_loss_limit 是 ExecContext 对象中的一个属性，用于设置止损的限价，即当触发止损时，希望股票以不低于这个限价的价格卖出。这里设置为当前收盘价减 2，意味着当股票价格下跌到当前收盘价减去 2 的价位时，触发止损并且以不低于这个价格的条件卖出股票，确保在一个相对理想的价格执行止损操作。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def buy_with_stop_and_limit(ctx):\n", "    if not ctx.long_pos():\n", "        ctx.buy_shares = ctx.calc_target_shares(1)\n", "        ctx.stop_loss_pct = 15\n", "        ctx.stop_loss_limit = ctx.close[-1] - 2  # 止损限价为当前收盘价减2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 止盈操作\n", "\n", "可以使用获利单来锁定交易的利润。以下代码在入场价格上涨 10% 处添加了一个获利单："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def buy_with_stop_loss_and_profit(ctx):\n", "    if not ctx.long_pos():\n", "        ctx.buy_shares = ctx.calc_target_shares(1)\n", "        ctx.stop_loss_pct = 20\n", "        ctx.stop_profit_pct = 10"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 策略内自定义数据\n", "\n", "ExecContext.session 是一个字典，执行期间每个时间点都持续存在的自定义数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def simple_strategy(ctx: ExecContext):\n", "    # 初始化 session 中的计数器\n", "    if 'counter' not in ctx.session:\n", "        ctx.session['counter'] = 0\n", "\n", "    # 每次执行策略时计数器加 1\n", "    ctx.session['counter'] += 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 直接买入\n"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "  5% (41 of 728) |#                      | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 12% (91 of 728) |##                     | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 20% (151 of 728) |####                  | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 30% (221 of 728) |######                | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 41% (301 of 728) |#########             | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 53% (391 of 728) |###########           | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 66% (481 of 728) |##############        | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 75% (551 of 728) |################      | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 86% (631 of 728) |###################   | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 99% (721 of 728) |##################### | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:01\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "\n", "    if not ctx.long_pos():\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>2963</td>\n", "      <td>NaN</td>\n", "      <td>17.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>1019</td>\n", "      <td>NaN</td>\n", "      <td>48.34</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                  \n", "1   buy  000001.SZ 2020-01-03    2963          NaN       17.12   0.0\n", "2   buy  002594.SZ 2020-01-03    1019          NaN       48.34   0.0"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"data": {"text/plain": ["name     end_market_value\n", "value           300860.49\n", "Name: 2, dtype: object"]}, "execution_count": 89, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.loc[2]"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["name       sharpe\n", "value    0.038323\n", "Name: 32, dtype: object"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.loc[32]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 简单策略1：各买入50%亏损10%卖出，然后不再交易"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 11% (81 of 728) |##                     | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 30% (221 of 728) |######                | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 49% (361 of 728) |##########            | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 71% (521 of 728) |###############       | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 93% (681 of 728) |####################  | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    if \"one_buy_sell\" not in ctx.session:\n", "        ctx.session[\"one_buy_sell\"] = False\n", "\n", "    if not ctx.long_pos() and not ctx.session[\"one_buy_sell\"]:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_loss_pct = 10\n", "        ctx.session[\"one_buy_sell\"] = True\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>2963</td>\n", "      <td>NaN</td>\n", "      <td>17.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>1019</td>\n", "      <td>NaN</td>\n", "      <td>48.34</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-23</td>\n", "      <td>2963</td>\n", "      <td>NaN</td>\n", "      <td>15.41</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  000001.SZ 2020-01-03    2963          NaN       17.12   0.0\n", "2    buy  002594.SZ 2020-01-03    1019          NaN       48.34   0.0\n", "3   sell  000001.SZ 2020-01-23    2963          NaN       15.41   0.0"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>1.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>100000.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>307521.31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>-5072.66</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>212593.97</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   name      value\n", "0           trade_count       1.00\n", "1  initial_market_value  100000.00\n", "2      end_market_value  307521.31\n", "3             total_pnl   -5072.66\n", "4        unrealized_pnl  212593.97"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 简单策略2：各买入50%亏损10%卖出或者赚20卖出，然后不再交易"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    if \"one_buy_sell\" not in ctx.session:\n", "        ctx.session[\"one_buy_sell\"] = False\n", "\n", "    if not ctx.long_pos() and not ctx.session[\"one_buy_sell\"]:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_trailing_pct = 10\n", "        ctx.stop_profit_pct = 20\n", "        ctx.session[\"one_buy_sell\"] = True\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>2963</td>\n", "      <td>NaN</td>\n", "      <td>17.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>1019</td>\n", "      <td>NaN</td>\n", "      <td>48.34</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-01-17</td>\n", "      <td>1019</td>\n", "      <td>NaN</td>\n", "      <td>58.01</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-23</td>\n", "      <td>2963</td>\n", "      <td>NaN</td>\n", "      <td>15.61</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  000001.SZ 2020-01-03    2963          NaN       17.12   0.0\n", "2    buy  002594.SZ 2020-01-03    1019          NaN       48.34   0.0\n", "3   sell  002594.SZ 2020-01-17    1019          NaN       58.01   0.0\n", "4   sell  000001.SZ 2020-01-23    2963          NaN       15.61   0.0"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>2.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>1.000000e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>1.053657e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>5.365710e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>5.456968e-12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   name         value\n", "0           trade_count  2.000000e+00\n", "1  initial_market_value  1.000000e+05\n", "2      end_market_value  1.053657e+05\n", "3             total_pnl  5.365710e+03\n", "4        unrealized_pnl  5.456968e-12"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 简单策略3：各买入50%，最高市场价下跌10%止损, 然后不再交易"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    if \"one_buy_sell\" not in ctx.session:\n", "        ctx.session[\"one_buy_sell\"] = False\n", "\n", "    if not ctx.long_pos() and not ctx.session[\"one_buy_sell\"]:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_trailing_pct = 10\n", "        ctx.session[\"one_buy_sell\"] = True\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest()"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>2963</td>\n", "      <td>NaN</td>\n", "      <td>17.12</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-01-03</td>\n", "      <td>1019</td>\n", "      <td>NaN</td>\n", "      <td>48.34</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-01-23</td>\n", "      <td>2963</td>\n", "      <td>NaN</td>\n", "      <td>15.61</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-02-03</td>\n", "      <td>1019</td>\n", "      <td>NaN</td>\n", "      <td>54.48</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  000001.SZ 2020-01-03    2963          NaN       17.12   0.0\n", "2    buy  002594.SZ 2020-01-03    1019          NaN       48.34   0.0\n", "3   sell  000001.SZ 2020-01-23    2963          NaN       15.61   0.0\n", "4   sell  002594.SZ 2020-02-03    1019          NaN       54.48   0.0"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>trade_count</td>\n", "      <td>2.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>initial_market_value</td>\n", "      <td>1.000000e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>end_market_value</td>\n", "      <td>1.017707e+05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>total_pnl</td>\n", "      <td>1.770680e+03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>unrealized_pnl</td>\n", "      <td>-7.275958e-12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   name         value\n", "0           trade_count  2.000000e+00\n", "1  initial_market_value  1.000000e+05\n", "2      end_market_value  1.017707e+05\n", "3             total_pnl  1.770680e+03\n", "4        unrealized_pnl -7.275958e-12"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.head(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 常用简单量化策略的pybroker实现\n", "\n", "### 突破策略\n", "- **策略原理**：当股票价格突破近期的高点或者低点时，产生相应的买入或卖出信号。\n", "- **具体操作**：\n", "    - **买入条件**：观察一段时间（如10个交易日）内的股票最高价，当今日收盘价突破这10日的最高价时，以收盘价买入股票。同时，设置止损价位为买入价下跌10%，止盈价位为买入价上涨20%。\n", "    - **卖出条件**：当股票价格跌破止损价位或者达到止盈价位时，卖出股票。另外，若观察到一段时间内的最低价被突破（即收盘价低于过去10日最低价），也可考虑卖出股票，以控制风险。\n"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 16% (121 of 728) |###                   | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 27% (201 of 728) |######                | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 46% (341 of 728) |##########            | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 66% (481 of 728) |##############        | Elapsed Time: 0:00:00 ETA:   0:00:00\n", " 90% (661 of 728) |###################   | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    high = ctx.high[-11:-1].max()\n", "    low = ctx.low[-11:-1].min()\n", "    #print(high, low)\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > high:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_loss_pct = 10\n", "        ctx.stop_profit_pct = 20\n", "    \n", "    if ctx.long_pos() and ctx.close[-1] < low:\n", "        ctx.sell_all_shares()\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-02-18</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>15.17</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-02-21</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>66.97</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2020-03-02</td>\n", "      <td>3253</td>\n", "      <td>NaN</td>\n", "      <td>14.70</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-03-09</td>\n", "      <td>744</td>\n", "      <td>NaN</td>\n", "      <td>60.27</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-04-01</td>\n", "      <td>779</td>\n", "      <td>NaN</td>\n", "      <td>58.84</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-11-14</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>11.99</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-11-21</td>\n", "      <td>341</td>\n", "      <td>NaN</td>\n", "      <td>249.76</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-02</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>264.02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>83</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2022-12-21</td>\n", "      <td>8075</td>\n", "      <td>NaN</td>\n", "      <td>12.86</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-12-26</td>\n", "      <td>322</td>\n", "      <td>NaN</td>\n", "      <td>257.85</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>84 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  000001.SZ 2020-02-18    3253          NaN       15.17   0.0\n", "2    buy  002594.SZ 2020-02-21     744          NaN       66.97   0.0\n", "3   sell  000001.SZ 2020-03-02    3253          NaN       14.70   0.0\n", "4   sell  002594.SZ 2020-03-09     744          NaN       60.27   0.0\n", "5    buy  002594.SZ 2020-04-01     779          NaN       58.84   0.0\n", "..   ...        ...        ...     ...          ...         ...   ...\n", "80   buy  000001.SZ 2022-11-14    8075          NaN       11.99   0.0\n", "81  sell  002594.SZ 2022-11-21     341          NaN      249.76   0.0\n", "82   buy  002594.SZ 2022-12-02     322          NaN      264.02   0.0\n", "83  sell  000001.SZ 2022-12-21    8075          NaN       12.86   0.0\n", "84  sell  002594.SZ 2022-12-26     322          NaN      257.85   0.0\n", "\n", "[84 rows x 7 columns]"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"text/plain": ["name     end_market_value\n", "value           187026.36\n", "Name: 2, dtype: object"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.loc[2]"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["name       sharpe\n", "value    0.046867\n", "Name: 32, dtype: object"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.loc[32]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 高低点反转策略\n", "- **策略原理**：基于股票价格在高低点之间的反转趋势进行操作。\n", "- **具体操作**：\n", "    - **买入条件**：当股票价格连续下跌后，出现当日最低价低于前几日的最低价，且当日收盘价高于当日最低价一定幅度（如5%），则认为可能出现反转，以当日收盘价买入股票。止损设置为买入价下跌8%，止盈设置为买入价上涨15%。\n", "    - **卖出条件**：当股票价格达到止盈价位或者跌破止损价位时卖出。此外，若买入后股票价格未能持续上涨，再次出现当日最高价低于前几日最高价，且收盘价低于当日最高价一定幅度（如3%），也可考虑卖出股票，避免进一步损失。"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Backtesting: 2020-01-01 00:00:00 to 2023-01-01 00:00:00\n", "\n", "Loaded cached bar data.\n", "\n", "Test split: 2020-01-02 00:00:00 to 2022-12-30 00:00:00\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0% (0 of 728) |                        | Elapsed Time: 0:00:00 ETA:  --:--:--\n", " 61% (451 of 728) |#############         | Elapsed Time: 0:00:00 ETA:   0:00:00\n", "100% (728 of 728) |######################| Elapsed Time: 0:00:00 Time:  0:00:00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Finished backtest: 0:00:00\n"]}], "source": ["def trying_strategy(ctx: ExecContext):\n", "    high = ctx.high[-6:-1].max()\n", "    low = ctx.low[-6:-1].min()\n", "    #print(high, low)\n", "\n", "    if not ctx.long_pos() and ctx.close[-1] > ctx.low[-1] * 1.05 and ctx.low[-1] < low:\n", "        ctx.buy_shares = ctx.calc_target_shares(0.5)\n", "        ctx.stop_loss_pct = 8\n", "        ctx.stop_profit_pct = 15\n", "\n", "    if ctx.long_pos() and ctx.close[-1] < ctx.high[-1] * 0.97 and ctx.high[-1] < high:\n", "        ctx.sell_all_shares()\n", "\n", "\n", "strategy = Strategy(akshare, start_date='2020-01-01', end_date='2023-01-01')\n", "strategy.add_execution(trying_strategy, ['000001.SZ','002594.SZ'])\n", "result = strategy.backtest(warmup=15)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>type</th>\n", "      <th>symbol</th>\n", "      <th>date</th>\n", "      <th>shares</th>\n", "      <th>limit_price</th>\n", "      <th>fill_price</th>\n", "      <th>fees</th>\n", "    </tr>\n", "    <tr>\n", "      <th>id</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-03-16</td>\n", "      <td>898</td>\n", "      <td>NaN</td>\n", "      <td>55.60</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2020-03-17</td>\n", "      <td>898</td>\n", "      <td>NaN</td>\n", "      <td>53.42</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-03-08</td>\n", "      <td>249</td>\n", "      <td>NaN</td>\n", "      <td>187.03</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-03-09</td>\n", "      <td>249</td>\n", "      <td>NaN</td>\n", "      <td>170.32</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-03-10</td>\n", "      <td>275</td>\n", "      <td>NaN</td>\n", "      <td>178.83</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-03-15</td>\n", "      <td>275</td>\n", "      <td>NaN</td>\n", "      <td>176.50</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-07-29</td>\n", "      <td>197</td>\n", "      <td>NaN</td>\n", "      <td>250.06</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-08-02</td>\n", "      <td>197</td>\n", "      <td>NaN</td>\n", "      <td>287.57</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-08-24</td>\n", "      <td>173</td>\n", "      <td>NaN</td>\n", "      <td>296.02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-08-27</td>\n", "      <td>173</td>\n", "      <td>NaN</td>\n", "      <td>288.02</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>buy</td>\n", "      <td>000001.SZ</td>\n", "      <td>2021-09-02</td>\n", "      <td>2775</td>\n", "      <td>NaN</td>\n", "      <td>18.29</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>sell</td>\n", "      <td>000001.SZ</td>\n", "      <td>2021-09-15</td>\n", "      <td>2775</td>\n", "      <td>NaN</td>\n", "      <td>20.09</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-11-30</td>\n", "      <td>171</td>\n", "      <td>NaN</td>\n", "      <td>305.50</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2021-12-07</td>\n", "      <td>171</td>\n", "      <td>NaN</td>\n", "      <td>298.72</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-03-31</td>\n", "      <td>217</td>\n", "      <td>NaN</td>\n", "      <td>230.94</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-04-12</td>\n", "      <td>217</td>\n", "      <td>NaN</td>\n", "      <td>234.88</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>buy</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-04-28</td>\n", "      <td>221</td>\n", "      <td>NaN</td>\n", "      <td>232.75</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>sell</td>\n", "      <td>002594.SZ</td>\n", "      <td>2022-05-13</td>\n", "      <td>221</td>\n", "      <td>NaN</td>\n", "      <td>267.66</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    type     symbol       date  shares  limit_price  fill_price  fees\n", "id                                                                   \n", "1    buy  002594.SZ 2020-03-16     898          NaN       55.60   0.0\n", "2   sell  002594.SZ 2020-03-17     898          NaN       53.42   0.0\n", "3    buy  002594.SZ 2021-03-08     249          NaN      187.03   0.0\n", "4   sell  002594.SZ 2021-03-09     249          NaN      170.32   0.0\n", "5    buy  002594.SZ 2021-03-10     275          NaN      178.83   0.0\n", "6   sell  002594.SZ 2021-03-15     275          NaN      176.50   0.0\n", "7    buy  002594.SZ 2021-07-29     197          NaN      250.06   0.0\n", "8   sell  002594.SZ 2021-08-02     197          NaN      287.57   0.0\n", "9    buy  002594.SZ 2021-08-24     173          NaN      296.02   0.0\n", "10  sell  002594.SZ 2021-08-27     173          NaN      288.02   0.0\n", "11   buy  000001.SZ 2021-09-02    2775          NaN       18.29   0.0\n", "12  sell  000001.SZ 2021-09-15    2775          NaN       20.09   0.0\n", "13   buy  002594.SZ 2021-11-30     171          NaN      305.50   0.0\n", "14  sell  002594.SZ 2021-12-07     171          NaN      298.72   0.0\n", "15   buy  002594.SZ 2022-03-31     217          NaN      230.94   0.0\n", "16  sell  002594.SZ 2022-04-12     217          NaN      234.88   0.0\n", "17   buy  002594.SZ 2022-04-28     221          NaN      232.75   0.0\n", "18  sell  002594.SZ 2022-05-13     221          NaN      267.66   0.0"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["result.orders"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/plain": ["name     end_market_value\n", "value           111652.36\n", "Name: 2, dtype: object"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["result.metrics_df.loc[2]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 收盘价偏离策略\n", "- **策略原理**：根据收盘价与近期价格的偏离程度来决定买卖操作。\n", "- **具体操作**：\n", "    - **买入条件**：计算过去N日（如15日）的平均收盘价，当今日收盘价低于该平均收盘价一定比例（如10%）时，买入股票。同时设置止损为买入价下跌7%，止盈为买入价上涨18%。\n", "    - **卖出条件**：当股票价格达到止盈价位或者跌破止损价位时进行卖出操作。如果在持有期间，收盘价连续多日（如3日）高于平均收盘价，也可考虑部分或全部卖出股票，落袋为安。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "quant", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}